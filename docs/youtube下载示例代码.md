
"""
YouTube 下载器模块
使用 yt-dlp 下载 YouTube 视频并提取音频
"""

import os
import re
import sys
import tempfile
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, Union
import yt_dlp

# 添加项目根目录到系统路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

from core.base import BaseProcessor
from core.logger_config import get_module_logger, setup_cli_logging


class YouTubeDownloader(BaseProcessor):
    """YouTube 音频下载器"""
    
    def __init__(self, config_name: str = "youtube_downloader", 
                 config_overrides: Optional[Dict[str, Any]] = None):
        """初始化下载器
        
        Args:
            config_name: 配置名称
            config_overrides: 配置覆盖字典（可选）
        """
        super().__init__(config_name, config_overrides=config_overrides)
        self.module_logger = get_module_logger("youtube_downloader")
        
        # 获取下载配置
        self.download_config = self.get_config_value("download", {})
        self.proxy_config = self.get_config_value("proxy", {})
        
        # 设置下载目录
        self.download_dir = Path(self.download_config.get(
            "output_directory", "data/downloads"
        ))
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置临时目录
        self.temp_dir = Path(self.download_config.get(
            "temp_directory", "data/temp"
        ))
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        self.module_logger.info("YouTube 下载器初始化完成")
    
    def is_youtube_url(self, url: str) -> bool:
        """检查是否是有效的 YouTube URL
        
        Args:
            url: 要检查的 URL
            
        Returns:
            bool: 是否是 YouTube URL
        """
        youtube_patterns = [
            r'(https?://)?(www\.)?(youtube\.com|youtu\.be)/',
            r'(https?://)?(www\.)?youtube\.com/(watch|embed|v|playlist)',
            r'(https?://)?(www\.)?youtu\.be/',
        ]
        
        for pattern in youtube_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        return False
    
    def _get_ydl_opts(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """获取 yt-dlp 配置选项
        
        Args:
            output_path: 输出文件路径（可选）
            
        Returns:
            Dict[str, Any]: yt-dlp 配置字典
        """
        # 基础配置
        ydl_opts = {
            'quiet': False,  # 显示下载信息
            'no_warnings': False,
            'extract_flat': False,
            'cachedir': str(self.temp_dir / '.cache'),
            'socket_timeout': 30,  # 30秒超时
            'retries': 3,  # 重试次数
        }
        
        # 代理配置
        if self.proxy_config.get("enabled", False):
            proxy_url = self.proxy_config.get("url")
            if proxy_url:
                ydl_opts['proxy'] = proxy_url
                self.module_logger.info(f"使用代理: {proxy_url}")
            else:
                self.module_logger.warning("代理已启用但未配置代理URL")
        
        # 音频提取配置
        audio_format = self.download_config.get("audio_format", "mp3")
        audio_quality = self.download_config.get("audio_quality", "192")
        
        # 设置格式选择
        ydl_opts.update({
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': audio_format,
                'preferredquality': audio_quality,
            }],
            'outtmpl': output_path or str(
                self.download_dir / '%(title)s.%(ext)s'
            ),
        })
        
        # 添加进度钩子
        ydl_opts['progress_hooks'] = [self._progress_hook]
        
        # 其他选项
        ydl_opts.update({
            'keepvideo': False,  # 不保留视频文件
            'writethumbnail': False,  # 不下载缩略图
            'writesubtitles': False,  # 不下载字幕
            'ignoreerrors': False,  # 遇到错误时停止
            'no_color': True,  # 禁用颜色输出
            'noprogress': False,  # 显示进度
        })
        
        return ydl_opts
    
    def _progress_hook(self, d: Dict[str, Any]) -> None:
        """下载进度回调
        
        Args:
            d: 进度信息字典
        """
        if d['status'] == 'downloading':
            total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
            downloaded = d.get('downloaded_bytes', 0)
            
            if total > 0:
                percent = (downloaded / total) * 100
                speed = d.get('speed', 0)
                if speed:
                    speed_mb = speed / (1024 * 1024)
                    self.module_logger.info(
                        f"下载进度: {percent:.1f}% | "
                        f"速度: {speed_mb:.1f} MB/s"
                    )
        
        elif d['status'] == 'finished':
            self.module_logger.info("下载完成，正在处理音频...")
    
    def download_audio(self, url: str, 
                      output_filename: Optional[str] = None) -> str:
        """下载 YouTube 视频的音频
        
        Args:
            url: YouTube 视频 URL
            output_filename: 输出文件名（可选）
            
        Returns:
            str: 下载的音频文件路径
            
        Raises:
            ValueError: 如果 URL 无效
            RuntimeError: 如果下载失败
        """
        if not self.is_youtube_url(url):
            raise ValueError(f"无效的 YouTube URL: {url}")
        
        self.module_logger.info(f"开始下载: {url}")
        
        try:
            # 构建获取信息的选项（包含代理）
            info_opts = {
                'quiet': True, 
                'no_warnings': True,
                'socket_timeout': 30,
            }
            
            # 如果启用了代理，添加代理配置
            if self.proxy_config.get("enabled", False):
                proxy_url = self.proxy_config.get("url")
                if proxy_url:
                    info_opts['proxy'] = proxy_url
                    self.module_logger.debug(f"获取视频信息时使用代理: {proxy_url}")
            
            # 获取视频信息
            with yt_dlp.YoutubeDL(info_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                video_title = info.get('title', 'unknown')
                video_id = info.get('id', 'unknown')
            
            self.module_logger.info(f"视频标题: {video_title}")
            
            # 设置输出路径
            if output_filename:
                # 去掉可能的扩展名，让 yt-dlp 添加正确的扩展名
                output_filename = Path(output_filename).stem
                output_path = str(self.download_dir / output_filename)
            else:
                # 使用安全的文件名
                safe_title = "".join(
                    c for c in video_title 
                    if c.isalnum() or c in (' ', '-', '_')
                ).rstrip()[:100]
                output_path = str(self.download_dir / safe_title)
            
            # 配置下载选项
            ydl_opts = self._get_ydl_opts(output_path)
            
            # 执行下载
            self.module_logger.info("开始下载音频...")
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            # 获取音频格式
            audio_format = self.download_config.get("audio_format", "mp3")
            
            # 查找可能的输出文件
            possible_paths = [
                # 可能的文件路径
                Path(f"{output_path}.{audio_format}"),
                Path(f"{output_path}.{audio_format}.{audio_format}"),
                Path(output_path).with_suffix(f".{audio_format}"),
                Path(f"{output_path}_na.{audio_format}"),
            ]
            
            # 添加所有可能的扩展名
            all_extensions = ['.mp3', '.m4a', '.wav', '.opus', '.ogg']
            for ext in all_extensions:
                possible_paths.append(Path(output_path).with_suffix(ext))
                
            # 检查下载目录中的所有文件
            import glob
            download_dir_files = glob.glob(str(self.download_dir / "*"))
            self.module_logger.debug(f"下载目录中的文件: {download_dir_files}")
            
            # 检查所有可能的路径
            for path in possible_paths:
                if path.exists():
                    self.module_logger.info(f"音频已保存到: {path}")
                    return str(path)
            
            # 检查下载目录中最新的文件
            if download_dir_files:
                newest_file = max(download_dir_files, key=os.path.getctime)
                self.module_logger.info(f"找到最新下载的文件: {newest_file}")
                return newest_file
            
            # 如果找不到文件，抛出错误
            raise RuntimeError(f"下载完成但找不到输出文件。尝试的路径: {', '.join(str(p) for p in possible_paths)}")
            
        except yt_dlp.utils.DownloadError as e:
            self.module_logger.error(f"下载失败: {e}")
            raise RuntimeError(f"YouTube 下载失败: {e}")
        except Exception as e:
            self.module_logger.error(f"下载过程中出现错误: {e}")
            raise
    
    def get_video_info(self, url: str) -> Dict[str, Any]:
        """获取 YouTube 视频信息
        
        Args:
            url: YouTube 视频 URL
            
        Returns:
            Dict[str, Any]: 视频信息字典
        """
        if not self.is_youtube_url(url):
            raise ValueError(f"无效的 YouTube URL: {url}")
        
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
            }
            
            # 添加代理
            if self.proxy_config.get("enabled", False):
                proxy_url = self.proxy_config.get("url")
                if proxy_url:
                    ydl_opts['proxy'] = proxy_url
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return {
                    'title': info.get('title', 'Unknown'),
                    'id': info.get('id'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'upload_date': info.get('upload_date'),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'description': info.get('description', ''),
                    'thumbnail': info.get('thumbnail'),
                    'url': url,
                }
                
        except Exception as e:
            self.module_logger.error(f"获取视频信息失败: {e}")
            raise RuntimeError(f"无法获取视频信息: {e}")
    
    def process(self, url: str, **kwargs) -> str:
        """处理 YouTube URL 并返回音频文件路径
        
        Args:
            url: YouTube 视频 URL
            **kwargs: 其他参数
            
        Returns:
            str: 下载的音频文件路径
        """
        return self.download_audio(url, **kwargs)


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description="YouTube 音频下载器")
    
    parser.add_argument("url", help="YouTube 视频 URL")
    parser.add_argument("-o", "--output", help="输出文件名")
    parser.add_argument("-p", "--proxy", help="代理地址 (如: socks5://************:9090)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细模式")
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_cli_logging(level=log_level)
    
    # 准备配置覆盖
    config_overrides = {}
    if args.proxy:
        config_overrides["proxy"] = {"enabled": True, "url": args.proxy}
    
    try:
        # 初始化下载器
        downloader = YouTubeDownloader(config_overrides=config_overrides)
        
        # 下载音频
        output_path = downloader.download_audio(args.url, args.output)
        
        print(f"\n✓ 下载成功: {output_path}")
        
        # 显示文件信息
        file_size = os.path.getsize(output_path)
        print(f"文件大小: {file_size / 1024:.1f} KB")
        
    except Exception as e:
        print(f"\n✗ 下载失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 
