# Arabic Learning Platform Backend

阿拉伯语学习平台后端 API 服务

## 功能特性

- 用户认证和授权
- 阅读材料管理
- 工具集成
- Qwen 服务调用管理
- 优先级队列和资源管理
- 断路器模式和故障容错

## 快速开始

### 安装依赖

```bash
poetry install
```

### 启动服务

```bash
poetry run python -m app.main
```

### API 文档

服务启动后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Qwen 服务集成

Backend 服务提供了统一的 Qwen 服务调用管理，包括：

- 优先级队列管理
- 资源分配和监控
- 断路器保护
- 重试机制
- 性能指标收集

### API 端点

- `POST /api/v1/qwen/generate` - 文本生成
- `POST /api/v1/qwen/validate` - 语义验证（audio-processor 兼容）
- `GET /api/v1/qwen/health` - 健康检查
- `GET /api/v1/qwen/status` - 系统状态
- `GET /api/v1/qwen/metrics` - 性能指标

## 配置

主要配置项在 `app/core/config.py` 中：

- `QWEN_SERVICE_API_URL` - Qwen 服务地址
- `QWEN_MAX_CONCURRENT_REQUESTS` - 最大并发请求数
- `QWEN_CIRCUIT_BREAKER_*` - 断路器配置
- `QWEN_RESOURCE_*` - 资源分配配置

## 开发

### 运行测试

```bash
poetry run pytest
```

### 代码格式化

```bash
poetry run ruff format
poetry run ruff check --fix
```
