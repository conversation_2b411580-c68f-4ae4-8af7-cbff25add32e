"""
应用配置
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-change-this"

    # 数据库配置
    DATABASE_URL: str = (
        "postgresql://arabic_user:password@localhost:5432/arabic_learning"
    )

    # Redis 配置
    REDIS_URL: str = "redis://localhost:6379/0"

    # CORS 配置
    ALLOWED_ORIGINS: list[str] = ["http://localhost:3000", "http://localhost:3001"]

    # JWT 配置
    JWT_SECRET_KEY: str = "jwt-secret-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30

    # Qwen Service 配置
    QWEN_SERVICE_API_URL: str = "http://localhost:8004"
    QWEN_SERVICE_TIMEOUT: float = 300.0  # 5 minutes default timeout
    QWEN_SERVICE_MAX_CONNECTIONS: int = 20
    QWEN_SERVICE_MAX_KEEPALIVE: int = 10
    QWEN_SERVICE_KEEPALIVE_EXPIRY: float = 30.0

    # Qwen Call Manager 配置
    QWEN_MAX_CONCURRENT_REQUESTS: int = 8  # Backend layer concurrency limit
    QWEN_HIGH_PRIORITY_TIMEOUT: float = 5.0  # High priority timeout (seconds)
    QWEN_MEDIUM_PRIORITY_TIMEOUT: float = 30.0  # Medium priority timeout
    QWEN_LOW_PRIORITY_TIMEOUT: float = 300.0  # Low priority timeout (5 minutes)

    # Circuit Breaker 配置
    QWEN_CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = 5  # Failures before opening
    QWEN_CIRCUIT_BREAKER_RECOVERY_TIMEOUT: float = 30.0  # Recovery timeout (seconds)
    QWEN_CIRCUIT_BREAKER_SUCCESS_THRESHOLD: int = 3  # Successes to close circuit

    # Resource Management 配置
    QWEN_RESOURCE_HIGH_PRIORITY_ALLOCATION: float = 0.3  # 30% for high priority
    QWEN_RESOURCE_MEDIUM_PRIORITY_ALLOCATION: float = 0.4  # 40% for medium priority
    QWEN_RESOURCE_LOW_PRIORITY_ALLOCATION: float = 0.3  # 30% for low priority
    QWEN_RESOURCE_MONITORING_INTERVAL: float = 5.0  # Resource monitoring interval

    # Retry 配置
    QWEN_MAX_RETRIES: int = 3
    QWEN_RETRY_BACKOFF_FACTOR: float = 2.0
    QWEN_RETRY_BASE_DELAY: float = 1.0

    class Config:
        env_file = ".env"
        case_sensitive = True


def get_settings() -> Settings:
    """获取配置实例"""
    return Settings()
