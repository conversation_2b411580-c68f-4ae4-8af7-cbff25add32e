"""
Dependency injection container implementation

Manages service lifecycle and provides dependency injection capabilities
for the backend service, following the clean architecture pattern.
"""

from typing import Any, Optional, TypeVar, cast

from loguru import logger

from app.interfaces.circuit_breaker_interface import Circuit<PERSON>reakerInterface
from app.interfaces.external_service_client_interface import (
    ExternalServiceClientInterface,
)
from app.services.qwen.interfaces import (
    QwenCallManagerInterface,
    QwenServiceInterface,
)
from app.shared.interfaces.queue_manager_interface import QueueManagerInterface
from app.shared.interfaces.resource_manager_interface import ResourceManagerInterface


T = TypeVar("T")


class DIContainer:
    """
    Dependency injection container for backend services

    Manages service instances and their lifecycle, providing a centralized
    way to resolve dependencies throughout the application.
    """

    def __init__(self) -> None:
        """Initialize the DI container"""
        self._services: dict[type, Any] = {}
        self._factory: Optional[Any] = None  # Will be BackendServiceFactory
        self._initialized = False

    def register_factory(self, factory: Any) -> None:
        """
        Register the service factory

        Args:
            factory: Service factory instance
        """
        self._factory = factory
        logger.debug("Backend service factory registered")

    def register_service(self, service_type: type[T], instance: T) -> None:
        """
        Register a service instance

        Args:
            service_type: Service interface type
            instance: Service implementation instance
        """
        self._services[service_type] = instance
        logger.debug(f"Service registered: {service_type.__name__}")

    def get_service(self, service_type: type[T]) -> T:
        """
        Get service instance by type

        Args:
            service_type: Service interface type

        Returns:
            Service implementation instance

        Raises:
            ValueError: If service type is not supported or factory not registered
        """
        # Check if service is already registered
        if service_type in self._services:
            return cast(T, self._services[service_type])

        # Create service using factory
        if self._factory is None:
            error_msg = "Service factory not registered"
            raise ValueError(error_msg)

        service_instance = self._create_service_from_factory(service_type)

        # Cache the service instance
        self._services[service_type] = service_instance
        return cast(T, service_instance)

    def _create_service_from_factory(self, service_type: type[T]) -> T:
        """
        Create service instance using factory

        Args:
            service_type: Service interface type

        Returns:
            Service implementation instance

        Raises:
            ValueError: If service type is not supported
        """
        if service_type == QwenServiceInterface:
            return cast(T, self._factory.create_qwen_service())
        if service_type == QwenCallManagerInterface:
            return cast(T, self._factory.create_call_manager())
        if service_type == QueueManagerInterface:
            return cast(T, self._factory.create_queue_manager())
        if service_type == ResourceManagerInterface:
            return cast(T, self._factory.create_resource_manager())
        if service_type == ExternalServiceClientInterface:
            return cast(T, self._factory.create_external_client())
        if service_type == CircuitBreakerInterface:
            return cast(T, self._factory.create_circuit_breaker())

        error_msg = f"Unsupported service type: {service_type}"
        raise ValueError(error_msg)

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization
        """
        if self._initialized:
            return

        if self._factory is None:
            error_msg = "Service factory not registered"
            raise ValueError(error_msg)

        try:
            logger.info("Initializing backend DI container services...")

            # Initialize the factory's services
            if hasattr(self._factory, "initialize_services"):
                await self._factory.initialize_services()

            self._initialized = True
            logger.success("Backend DI container services initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize DI container services: {e}")
            raise

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up backend DI container services...")

            # Cleanup factory services
            if self._factory and hasattr(self._factory, "cleanup_services"):
                await self._factory.cleanup_services()

            # Cleanup individual services
            for service_type, service_instance in self._services.items():
                if hasattr(service_instance, "cleanup"):
                    try:
                        await service_instance.cleanup()
                        logger.debug(f"Cleaned up service: {service_type.__name__}")
                    except Exception as e:
                        logger.warning(
                            f"Error cleaning up {service_type.__name__}: {e}"
                        )

            self._services.clear()
            self._initialized = False
            logger.success("Backend DI container services cleaned up successfully")

        except Exception as e:
            logger.error(f"Error during DI container cleanup: {e}")
            raise

    def is_initialized(self) -> bool:
        """
        Check if container is initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized

    def get_registered_services(self) -> dict[str, str]:
        """
        Get list of registered services

        Returns:
            Dictionary mapping service type names to implementation class names
        """
        return {
            service_type.__name__: type(instance).__name__
            for service_type, instance in self._services.items()
        }
