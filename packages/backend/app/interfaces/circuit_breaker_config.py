"""
Circuit breaker configuration data class

Contains configuration parameters for circuit breaker pattern implementation,
including failure thresholds, recovery timeouts, and success criteria.
"""

from dataclasses import dataclass


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int
    recovery_timeout: float
    expected_exception: type
    success_threshold: int
