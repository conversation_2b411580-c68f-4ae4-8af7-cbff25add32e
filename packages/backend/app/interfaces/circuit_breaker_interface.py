"""
Circuit breaker interface

Protocol definition for circuit breaker pattern implementation,
providing fault tolerance and resilience for external service calls.
"""

from typing import Protocol, runtime_checkable, Dict, Any


@runtime_checkable
class CircuitBreakerInterface(Protocol):
    """Protocol for circuit breaker pattern implementation"""

    async def call(self, func, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            RuntimeError: If circuit is open or function fails
        """
        ...

    def is_open(self) -> bool:
        """
        Check if circuit breaker is open

        Returns:
            True if circuit is open, False otherwise
        """
        ...

    def get_state(self) -> str:
        """
        Get current circuit breaker state

        Returns:
            Current state as string ('closed', 'open', 'half_open')
        """
        ...

    def get_stats(self) -> Dict[str, Any]:
        """
        Get circuit breaker statistics

        Returns:
            Dictionary containing failure counts, success rates, etc.
        """
        ...

    async def reset(self) -> None:
        """
        Reset circuit breaker to closed state
        """
        ...
