"""
External service client interface

Protocol definition for external service HTTP client operations,
enabling clean abstraction and testability for service communication.
"""

from typing import Protocol, runtime_checkable, Optional, Dict, Any
import httpx


@runtime_checkable
class ExternalServiceClientInterface(Protocol):
    """Protocol for external service HTTP client operations"""

    async def post(
        self,
        endpoint: str,
        data: Dict[str, Any],
        timeout: Optional[float] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make POST request to external service

        Args:
            endpoint: API endpoint path
            data: Request payload
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        ...

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make GET request to external service

        Args:
            endpoint: API endpoint path
            params: Query parameters
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        ...

    async def health_check(self) -> bool:
        """
        Check if external service is healthy

        Returns:
            True if service is healthy, False otherwise
        """
        ...

    async def get_client_stats(self) -> Dict[str, Any]:
        """
        Get HTTP client statistics

        Returns:
            Dictionary containing connection pool stats and metrics
        """
        ...

    async def close(self) -> None:
        """
        Close HTTP client and cleanup connections
        """
        ...
