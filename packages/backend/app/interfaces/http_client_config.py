"""
HTTP client configuration data class

Contains configuration parameters for HTTP client setup,
including connection limits, timeouts, and retry settings.
"""

from dataclasses import dataclass


@dataclass
class HttpClientConfig:
    """Configuration for HTTP client"""
    base_url: str
    timeout: float
    max_connections: int
    max_keepalive_connections: int
    keepalive_expiry: float
    retries: int
    backoff_factor: float
