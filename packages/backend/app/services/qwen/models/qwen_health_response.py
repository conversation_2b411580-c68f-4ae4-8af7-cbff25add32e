"""
Qwen health response model

Contains the QwenHealthResponse model for qwen service API,
providing validation and serialization for health check responses.
"""

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class QwenHealthResponse(BaseModel):
    """API model for health check responses"""

    healthy: bool = Field(..., description="Whether the service is healthy")
    service: str = Field("qwen-call-manager", description="Service name")
    timestamp: str = Field(..., description="Health check timestamp")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional health details"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "healthy": True,
                "service": "qwen-call-manager",
                "timestamp": "2024-01-01T12:00:00Z",
                "details": {
                    "qwen_service_healthy": True,
                    "queue_status": "normal",
                    "circuit_breaker": "closed",
                },
            }
        }
