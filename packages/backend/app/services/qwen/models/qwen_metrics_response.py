"""
Qwen metrics response model

Contains the QwenMetricsResponse model for qwen service API,
providing validation and serialization for metrics responses.
"""

from pydantic import BaseModel, Field

from .metrics_data_api import MetricsDataAPI


class QwenMetricsResponse(BaseModel):
    """API model for metrics responses"""

    current: MetricsDataAPI = Field(..., description="Current metrics")
    last_hour: MetricsDataAPI = Field(..., description="Last hour metrics")
    last_day: MetricsDataAPI = Field(..., description="Last day metrics")
    timestamp: str = Field(..., description="Metrics collection timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "current": {
                    "requests_per_minute": 15.5,
                    "average_response_time": 2.3,
                    "success_rate": 95.0,
                    "error_rate": 5.0,
                    "queue_wait_time": 0.5,
                    "gpu_utilization": 70.0,
                },
                "last_hour": {
                    "requests_per_minute": 12.8,
                    "average_response_time": 2.1,
                    "success_rate": 96.5,
                    "error_rate": 3.5,
                    "queue_wait_time": 0.3,
                    "gpu_utilization": 65.0,
                },
                "last_day": {
                    "requests_per_minute": 10.2,
                    "average_response_time": 2.0,
                    "success_rate": 97.0,
                    "error_rate": 3.0,
                    "queue_wait_time": 0.2,
                    "gpu_utilization": 60.0,
                },
                "timestamp": "2024-01-01T12:00:00Z",
            }
        }
