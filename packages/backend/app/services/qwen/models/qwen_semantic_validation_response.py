"""
Qwen semantic validation response model

Contains the QwenSemanticValidationResponse model for qwen service API,
providing validation and serialization for semantic validation responses.
"""

from typing import Optional

from pydantic import BaseModel, Field


class QwenSemanticValidationResponse(BaseModel):
    """API model for semantic validation responses (audio-processor compatibility)"""

    success: bool = Field(..., description="Whether the validation was successful")
    message: str = Field(..., description="Response message or error description")
    generated_text: str = Field(..., description="Validation result text")
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Semantic validation completed",
                "generated_text": "Yes, this is a valid Arabic chapter title",
                "processing_time": 1.2,
            }
        }
