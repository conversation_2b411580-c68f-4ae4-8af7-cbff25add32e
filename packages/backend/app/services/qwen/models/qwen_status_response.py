"""
Qwen status response model

Contains the QwenStatusResponse model for qwen service API,
providing validation and serialization for system status responses.
"""

from pydantic import BaseModel, Field

from .queue_status_api import QueueStatusAPI
from .resource_status_api import ResourceStatusAPI


class QwenStatusResponse(BaseModel):
    """API model for system status responses"""

    queue_status: QueueStatusAPI = Field(..., description="Current queue status")
    resource_status: ResourceStatusAPI = Field(
        ..., description="Current resource status"
    )
    uptime: float = Field(..., description="Service uptime in seconds")
    total_requests: int = Field(..., description="Total requests processed")
    successful_requests: int = Field(..., description="Number of successful requests")
    failed_requests: int = Field(..., description="Number of failed requests")

    class Config:
        json_schema_extra = {
            "example": {
                "queue_status": {
                    "high_priority_count": 2,
                    "medium_priority_count": 5,
                    "low_priority_count": 10,
                    "total_pending": 17,
                    "average_wait_time": 2.5,
                    "processing_count": 1,
                },
                "resource_status": {
                    "gpu_utilization": 75.5,
                    "memory_usage": 60.2,
                    "active_connections": 8,
                    "max_connections": 20,
                    "circuit_breaker_state": "closed",
                    "last_health_check": "2024-01-01T12:00:00Z",
                },
                "uptime": 86400.0,
                "total_requests": 1000,
                "successful_requests": 950,
                "failed_requests": 50,
            }
        }
