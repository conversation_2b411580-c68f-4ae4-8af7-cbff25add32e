"""
Qwen call manager service implementation

Main orchestration service for qwen service calls, implementing priority queuing,
resource management, circuit breaker patterns, and comprehensive monitoring.
"""

import asyncio
import time
from typing import Any, Optional

from loguru import logger

from app.core.settings import Settings
from app.interfaces.external_service_client_interface import (
    ExternalServiceClientInterface,
)
from app.services.qwen.adapters import QwenResponseAdapter
from app.services.qwen.interfaces import (
    GenerationRequest,
    GenerationResponse,
    QwenCallManagerInterface,
    SemanticValidationRequest,
    SemanticValidationResponse,
)
from app.shared.interfaces.queue_manager_interface import QueueManagerInterface
from app.shared.interfaces.resource_manager_interface import ResourceManagerInterface


class QwenCallManagerImpl(QwenCallManagerInterface):
    """
    Implementation of unified qwen service call management

    Orchestrates the complete workflow for qwen service calls including
    priority queuing, resource allocation, circuit breaker protection,
    and comprehensive monitoring and metrics collection.
    """

    def __init__(
        self,
        settings: Settings,
        queue_manager: QueueManagerInterface,
        resource_manager: ResourceManagerInterface,
        external_client: ExternalServiceClientInterface,
    ):
        """
        Initialize call manager with all dependencies

        Args:
            settings: Application settings containing configuration
            queue_manager: Queue management service for priority handling
            resource_manager: Resource management service for GPU allocation
            external_client: HTTP client for qwen-service communication
        """
        self.settings = settings
        self.queue_manager = queue_manager
        self.resource_manager = resource_manager
        self.external_client = external_client

        # Processing state
        self._processing_requests: dict[str, GenerationRequest] = {}
        self._completed_requests: dict[str, GenerationResponse] = {}
        self._processing_lock = asyncio.Lock()

        # Background task for request processing
        self._processor_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()

        # Metrics
        self._start_time = time.time()
        self._total_processed = 0

        logger.debug("QwenCallManager initialized")

    async def process_request(self, request: GenerationRequest) -> GenerationResponse:
        """
        Process generation request through complete workflow

        Args:
            request: Generation request with priority and configuration

        Returns:
            GenerationResponse containing result and metadata

        Raises:
            RuntimeError: If processing fails
        """
        start_time = time.time()

        try:
            # Enqueue request
            request_id = await self.queue_manager.enqueue_request(
                request, request.priority
            )

            logger.debug(f"Request {request_id} enqueued for processing")

            # Wait for processing completion
            result = await self._wait_for_completion(request_id, request.timeout)

            # Record success metrics
            processing_time = time.time() - start_time
            await self.resource_manager.record_success(processing_time)

            return result

        except Exception as e:
            # Record failure metrics
            await self.resource_manager.record_failure(e)

            error_msg = f"Failed to process request: {e}"
            logger.error(error_msg)

            return GenerationResponse(
                success=False,
                message=error_msg,
                generated_text="",
                processing_time=time.time() - start_time,
                request_id=request.request_id,
            )

    async def process_validation(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Process semantic validation request (audio-processor compatibility)

        Args:
            request: Semantic validation request

        Returns:
            SemanticValidationResponse containing validation result

        Raises:
            RuntimeError: If validation fails
        """
        start_time = time.time()

        try:
            # Convert to generation request
            generation_request = GenerationRequest(
                prompt=request.prompt,
                system_prompt=request.system_prompt,
                enable_thinking=request.enable_thinking,
                priority=request.priority,
                timeout=request.timeout,
                request_id=request.request_id,
                source_service="audio-processor",
            )

            # Process through normal workflow
            generation_response = await self.process_request(generation_request)

            # Convert back to validation response
            return SemanticValidationResponse(
                success=generation_response.success,
                message=generation_response.message,
                generated_text=generation_response.generated_text,
                processing_time=generation_response.processing_time,
                queue_time=generation_response.queue_time,
            )

        except Exception as e:
            error_msg = f"Failed to process validation request: {e}"
            logger.error(error_msg)

            return SemanticValidationResponse(
                success=False,
                message=error_msg,
                generated_text="",
                processing_time=time.time() - start_time,
            )

    async def get_system_status(self) -> dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary containing queue status, resource status, and metrics
        """
        try:
            # Get status from all components
            queue_status = await self.queue_manager.get_queue_status()
            resource_status = await self.resource_manager.get_resource_status()

            # Calculate uptime
            uptime = time.time() - self._start_time

            # Get processing metrics
            async with self._processing_lock:
                currently_processing = len(self._processing_requests)

            return {
                "queue_status": {
                    "high_priority_count": queue_status.high_priority_count,
                    "medium_priority_count": queue_status.medium_priority_count,
                    "low_priority_count": queue_status.low_priority_count,
                    "total_pending": queue_status.total_pending,
                    "average_wait_time": queue_status.average_wait_time,
                    "processing_count": currently_processing,
                },
                "resource_status": {
                    "gpu_utilization": resource_status.gpu_utilization,
                    "memory_usage": resource_status.memory_usage,
                    "active_connections": resource_status.active_connections,
                    "max_connections": resource_status.max_connections,
                    "circuit_breaker_state": resource_status.circuit_breaker_state.value,
                    "last_health_check": resource_status.last_health_check.isoformat(),
                },
                "uptime": uptime,
                "total_requests": self._total_processed,
                "successful_requests": 0,  # Will be implemented with metrics
                "failed_requests": 0,  # Will be implemented with metrics
            }

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "error": f"Failed to get system status: {e}",
                "uptime": time.time() - self._start_time,
            }

    async def initialize(self) -> None:
        """
        Initialize call manager and all dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info("Initializing QwenCallManager...")

            # Start background processor
            self._processor_task = asyncio.create_task(self._background_processor())

            logger.success("QwenCallManager initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize QwenCallManager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup(self) -> None:
        """
        Cleanup call manager and release all resources
        """
        try:
            logger.info("Cleaning up QwenCallManager...")

            # Signal shutdown
            self._shutdown_event.set()

            # Cancel background processor
            if self._processor_task and not self._processor_task.done():
                self._processor_task.cancel()
                try:
                    await self._processor_task
                except asyncio.CancelledError:
                    pass

            # Clear processing state
            async with self._processing_lock:
                self._processing_requests.clear()
                self._completed_requests.clear()

            logger.success("QwenCallManager cleaned up successfully")

        except Exception as e:
            logger.error(f"Error during QwenCallManager cleanup: {e}")
            raise

    async def _wait_for_completion(
        self, request_id: str, timeout: Optional[float]
    ) -> GenerationResponse:
        """
        Wait for request completion with timeout

        Args:
            request_id: ID of request to wait for
            timeout: Maximum wait time in seconds

        Returns:
            GenerationResponse when request completes

        Raises:
            asyncio.TimeoutError: If request times out
            RuntimeError: If request fails
        """
        # Use default timeout if not specified
        if timeout is None:
            timeout = self.settings.QWEN_SERVICE_TIMEOUT

        start_time = time.time()

        while time.time() - start_time < timeout:
            # Check if request is completed
            async with self._processing_lock:
                if request_id in self._completed_requests:
                    result = self._completed_requests.pop(request_id)
                    return result

            # Wait a bit before checking again
            await asyncio.sleep(0.1)

        # Timeout occurred
        await self.queue_manager.cancel_request(request_id)
        raise asyncio.TimeoutError(f"Request {request_id} timed out after {timeout}s")

    async def _background_processor(self) -> None:
        """
        Background task for processing queued requests

        Continuously processes requests from the queue, managing
        resource allocation and calling the external qwen service.
        """
        logger.info("Background request processor started")

        try:
            while not self._shutdown_event.is_set():
                try:
                    # Get next request from queue
                    request = await self.queue_manager.dequeue_request()

                    if request is None:
                        # No requests available, wait a bit
                        await asyncio.sleep(0.1)
                        continue

                    # Process request in background
                    asyncio.create_task(self._process_single_request(request))

                except Exception as e:
                    logger.error(f"Error in background processor: {e}")
                    await asyncio.sleep(1.0)  # Wait before retrying

        except asyncio.CancelledError:
            logger.info("Background processor cancelled")
            raise
        except Exception as e:
            logger.error(f"Background processor failed: {e}")
            raise
        finally:
            logger.info("Background request processor stopped")

    async def _process_single_request(self, request: GenerationRequest) -> None:
        """
        Process a single request through the complete workflow

        Args:
            request: Generation request to process
        """
        request_id = request.request_id or "unknown"
        queue_start_time = time.time()

        try:
            # Track processing request
            async with self._processing_lock:
                self._processing_requests[request_id] = request

            # Acquire resource
            resource_acquired = await self.resource_manager.acquire_resource(
                request.priority
            )

            if not resource_acquired:
                # Resource unavailable
                result = GenerationResponse(
                    success=False,
                    message="Resource unavailable - system overloaded",
                    generated_text="",
                    processing_time=0.0,
                    queue_time=time.time() - queue_start_time,
                    request_id=request_id,
                )
            else:
                try:
                    # Make actual call to qwen-service
                    processing_start_time = time.time()

                    qwen_response = await self.external_client.post(
                        endpoint="/api/v1/generate",
                        data={
                            "prompt": request.prompt,
                            "system_prompt": request.system_prompt,
                            "enable_thinking": request.enable_thinking,
                        },
                        timeout=request.timeout,
                    )

                    processing_time = time.time() - processing_start_time
                    queue_time = processing_start_time - queue_start_time

                    # Convert response
                    result = QwenResponseAdapter.qwen_service_to_domain_response(
                        qwen_response, request_id, queue_time
                    )
                    result.processing_time = processing_time

                    logger.debug(
                        f"Request {request_id} completed successfully "
                        f"(processing: {processing_time:.2f}s, queue: {queue_time:.2f}s)"
                    )

                finally:
                    # Always release resource
                    await self.resource_manager.release_resource()

            # Store completed result
            async with self._processing_lock:
                self._completed_requests[request_id] = result
                self._processing_requests.pop(request_id, None)
                self._total_processed += 1

        except Exception as e:
            logger.error(f"Error processing request {request_id}: {e}")

            # Create error response
            error_result = GenerationResponse(
                success=False,
                message=f"Processing failed: {e}",
                generated_text="",
                processing_time=0.0,
                queue_time=time.time() - queue_start_time,
                request_id=request_id,
            )

            # Store error result
            async with self._processing_lock:
                self._completed_requests[request_id] = error_result
                self._processing_requests.pop(request_id, None)
