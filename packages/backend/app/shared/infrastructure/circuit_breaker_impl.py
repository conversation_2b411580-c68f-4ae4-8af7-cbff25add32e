"""
Circuit breaker implementation

Implements the circuit breaker pattern for fault tolerance and system protection,
providing automatic failure detection and recovery mechanisms.
"""

import asyncio
import time
from typing import Any, Callable

from loguru import logger

from app.core.settings import Settings
from app.shared.interfaces.circuit_breaker_interface import CircuitBreakerInterface

from .circuit_state import CircuitState


class CircuitBreakerImpl(CircuitBreakerInterface):
    """
    Implementation of circuit breaker pattern for fault tolerance

    Monitors service health and automatically opens the circuit when
    failure thresholds are exceeded, providing fast failure and
    automatic recovery testing.
    """

    def __init__(self, settings: Settings):
        """
        Initialize circuit breaker with configuration

        Args:
            settings: Application settings containing circuit breaker configuration
        """
        self.settings = settings

        # Configuration
        self.failure_threshold = settings.QWEN_CIRCUIT_BREAKER_FAILURE_THRESHOLD
        self.recovery_timeout = settings.QWEN_CIRCUIT_BREAKER_RECOVERY_TIMEOUT
        self.success_threshold = settings.QWEN_CIRCUIT_BREAKER_SUCCESS_THRESHOLD

        # State
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time = 0.0
        self._next_attempt_time = 0.0

        # Statistics
        self._total_calls = 0
        self._total_failures = 0
        self._total_successes = 0
        self._state_changes = 0

        # Lock for thread safety
        self._lock = asyncio.Lock()

        logger.debug(
            f"CircuitBreaker initialized (failure_threshold: {self.failure_threshold}, "
            f"recovery_timeout: {self.recovery_timeout}s)"
        )

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            RuntimeError: If circuit is open or function fails
        """
        async with self._lock:
            self._total_calls += 1

            # Check if circuit should allow the call
            if not await self._should_allow_request():
                error_msg = "Circuit breaker is open - request rejected"
                logger.debug(error_msg)
                raise RuntimeError(error_msg)

        try:
            # Execute the function
            result = (
                await func(*args, **kwargs)
                if asyncio.iscoroutinefunction(func)
                else func(*args, **kwargs)
            )

            # Record success
            await self._record_success()

            return result

        except Exception as e:
            # Record failure
            await self._record_failure(e)
            raise

    def is_open(self) -> bool:
        """
        Check if circuit breaker is open

        Returns:
            True if circuit is open, False otherwise
        """
        return self._state == CircuitState.OPEN

    def get_state(self) -> str:
        """
        Get current circuit breaker state

        Returns:
            Current state as string ('closed', 'open', 'half_open')
        """
        return self._state.value

    def get_stats(self) -> dict[str, Any]:
        """
        Get circuit breaker statistics

        Returns:
            Dictionary containing failure counts, success rates, etc.
        """
        return {
            "state": self._state.value,
            "failure_count": self._failure_count,
            "success_count": self._success_count,
            "total_calls": self._total_calls,
            "total_failures": self._total_failures,
            "total_successes": self._total_successes,
            "state_changes": self._state_changes,
            "failure_rate": (
                (self._total_failures / self._total_calls * 100.0)
                if self._total_calls > 0
                else 0.0
            ),
            "last_failure_time": self._last_failure_time,
            "next_attempt_time": self._next_attempt_time,
        }

    async def reset(self) -> None:
        """
        Reset circuit breaker to closed state
        """
        async with self._lock:
            logger.info("Resetting circuit breaker to closed state")

            old_state = self._state
            self._state = CircuitState.CLOSED
            self._failure_count = 0
            self._success_count = 0
            self._last_failure_time = 0.0
            self._next_attempt_time = 0.0

            if old_state != CircuitState.CLOSED:
                self._state_changes += 1
                logger.info(
                    f"Circuit breaker state changed: {old_state.value} -> {self._state.value}"
                )

    async def _should_allow_request(self) -> bool:
        """
        Check if request should be allowed based on current state

        Returns:
            True if request should be allowed, False otherwise
        """
        current_time = time.time()

        if self._state == CircuitState.CLOSED:
            return True
        elif self._state == CircuitState.OPEN:
            # Check if recovery timeout has passed
            if current_time >= self._next_attempt_time:
                logger.debug("Circuit breaker transitioning to half-open state")
                self._state = CircuitState.HALF_OPEN
                self._success_count = 0
                self._state_changes += 1
                return True
            return False
        elif self._state == CircuitState.HALF_OPEN:
            return True

        return False

    async def _record_success(self) -> None:
        """
        Record successful request and update circuit state
        """
        async with self._lock:
            self._total_successes += 1

            if self._state == CircuitState.HALF_OPEN:
                self._success_count += 1

                # Check if we have enough successes to close the circuit
                if self._success_count >= self.success_threshold:
                    logger.info(
                        "Circuit breaker closing - sufficient successes recorded"
                    )
                    old_state = self._state
                    self._state = CircuitState.CLOSED
                    self._failure_count = 0
                    self._success_count = 0
                    self._state_changes += 1
                    logger.info(
                        f"Circuit breaker state changed: {old_state.value} -> {self._state.value}"
                    )

            elif self._state == CircuitState.CLOSED:
                # Reset failure count on success
                self._failure_count = 0

    async def _record_failure(self, error: Exception) -> None:
        """
        Record failed request and update circuit state

        Args:
            error: Exception that caused the failure
        """
        async with self._lock:
            self._total_failures += 1
            self._failure_count += 1
            self._last_failure_time = time.time()

            logger.debug(f"Circuit breaker recorded failure: {error}")

            # Check if we should open the circuit
            if (
                self._state in [CircuitState.CLOSED, CircuitState.HALF_OPEN]
                and self._failure_count >= self.failure_threshold
            ):
                logger.warning(
                    f"Circuit breaker opening - failure threshold reached "
                    f"({self._failure_count}/{self.failure_threshold})"
                )

                old_state = self._state
                self._state = CircuitState.OPEN
                self._next_attempt_time = time.time() + self.recovery_timeout
                self._state_changes += 1

                logger.warning(
                    f"Circuit breaker state changed: {old_state.value} -> {self._state.value} "
                    f"(next attempt at: {self._next_attempt_time})"
                )
