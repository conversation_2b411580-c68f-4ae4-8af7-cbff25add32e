"""
Generic call manager interface

Defines the protocol for service call management with priority queuing,
resource allocation, and monitoring capabilities.
"""

from typing import Any, Dict, Generic, Protocol, TypeVar, runtime_checkable

TRequest = TypeVar('TRequest')
TResponse = TypeVar('TResponse')


@runtime_checkable
class CallManagerInterface(Protocol, Generic[TRequest, TResponse]):
    """Generic protocol for service call management"""

    async def process_request(self, request: TRequest) -> TResponse:
        """
        Process service request through complete workflow

        Args:
            request: Service request with priority and configuration

        Returns:
            Service response containing result and metadata

        Raises:
            RuntimeError: If processing fails
        """
        ...

    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary containing queue status, resource status, and metrics
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize call manager and all dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup call manager and release all resources
        """
        ...
