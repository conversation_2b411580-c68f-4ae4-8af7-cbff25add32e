"""
Generic circuit breaker interface

Defines the protocol for circuit breaker functionality with fault tolerance
and automatic recovery capabilities for external service calls.
"""

from typing import Any, Callable, Protocol, runtime_checkable


@runtime_checkable
class CircuitBreakerInterface(Protocol):
    """Generic protocol for circuit breaker functionality"""

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            CircuitBreakerOpenError: If circuit is open
            Exception: Original function exceptions
        """
        ...

    def is_open(self) -> bool:
        """
        Check if circuit breaker is open

        Returns:
            True if circuit is open, False otherwise
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize circuit breaker

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup circuit breaker and release resources
        """
        ...
