"""
Generic external service client interface

Defines the protocol for HTTP client operations to external services
with health checking and error handling capabilities.
"""

from typing import Any, Dict, Optional, Protocol, runtime_checkable


@runtime_checkable
class ExternalServiceClientInterface(Protocol):
    """Generic protocol for external service HTTP client operations"""

    async def post(
        self,
        endpoint: str,
        data: Dict[str, Any],
        timeout: Optional[float] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make POST request to external service

        Args:
            endpoint: API endpoint path
            data: Request payload
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        ...

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make GET request to external service

        Args:
            endpoint: API endpoint path
            params: Query parameters
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        ...

    async def health_check(self) -> bool:
        """
        Check if external service is healthy

        Returns:
            True if service is healthy, False otherwise
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize external service client

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup external service client
        """
        ...
