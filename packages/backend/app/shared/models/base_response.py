"""
Base response data class

Contains the fundamental structure for service responses,
including success status, timing information, and identification.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class BaseResponse:
    """Base class for service responses"""
    success: bool
    message: str
    processing_time: float
    queue_time: float = 0.0
    request_id: Optional[str] = None
