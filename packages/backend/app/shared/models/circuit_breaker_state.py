"""
Circuit breaker state enumeration

Defines the possible states for circuit breaker pattern implementation,
providing fault tolerance and resilience for service operations.
"""

from enum import Enum


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, requests fail fast
    HALF_OPEN = "half_open"  # Testing if service has recovered
