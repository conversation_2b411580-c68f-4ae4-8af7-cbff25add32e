"""
Queue status data class

Contains queue status information and metrics,
including counts, wait times, and overload indicators.
"""

from dataclasses import dataclass


@dataclass
class QueueStatus:
    """Queue status information"""
    high_priority_count: int
    medium_priority_count: int
    low_priority_count: int
    total_pending: int
    average_wait_time: float
    processing_count: int = 0
    
    @property
    def is_empty(self) -> bool:
        """Check if all queues are empty"""
        return self.total_pending == 0
    
    @property
    def is_overloaded(self) -> bool:
        """Check if system appears overloaded"""
        return self.average_wait_time > 30.0 or self.total_pending > 100
