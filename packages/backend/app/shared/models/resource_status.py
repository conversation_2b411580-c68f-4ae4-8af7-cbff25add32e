"""
Resource status data class

Contains resource allocation and usage status information,
including utilization metrics and health indicators.
"""

from dataclasses import dataclass
from datetime import datetime

from .circuit_breaker_state import CircuitBreakerState


@dataclass
class ResourceStatus:
    """Resource allocation and usage status"""
    gpu_utilization: float
    memory_usage: float
    active_connections: int
    max_connections: int
    circuit_breaker_state: CircuitBreakerState
    last_health_check: datetime
    
    @property
    def utilization_percentage(self) -> float:
        """Get resource utilization as percentage"""
        if self.max_connections == 0:
            return 0.0
        return (self.active_connections / self.max_connections) * 100.0
    
    @property
    def is_healthy(self) -> bool:
        """Check if resource status indicates healthy system"""
        return (
            self.circuit_breaker_state == CircuitBreakerState.CLOSED and
            self.utilization_percentage < 90.0 and
            self.memory_usage < 90.0
        )
    
    @property
    def is_overloaded(self) -> bool:
        """Check if system appears overloaded"""
        return (
            self.utilization_percentage > 95.0 or
            self.memory_usage > 95.0 or
            self.circuit_breaker_state == CircuitBreakerState.OPEN
        )
