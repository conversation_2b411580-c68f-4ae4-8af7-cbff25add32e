"""
Generic call manager service implementation

Main orchestration service for service calls, implementing priority queuing,
resource management, circuit breaker patterns, and comprehensive monitoring.
"""

import asyncio
import time
from collections.abc import Awaitable
from typing import Any, Callable, Optional, TypeVar

from loguru import logger

from app.shared.interfaces.call_manager_interface import CallManagerInterface
from app.shared.interfaces.external_service_client_interface import ExternalServiceClientInterface
from app.shared.interfaces.queue_manager_interface import QueueManagerInterface
from app.shared.interfaces.resource_manager_interface import ResourceManagerInterface
from app.shared.models.base_request import BaseRequest
from app.shared.models.base_response import BaseResponse
from app.shared.models.service_config import ServiceConfig


TRequest = TypeVar("TRequest", bound=BaseRequest)
TResponse = TypeVar("TResponse", bound=BaseResponse)


class GenericCallManager(CallManagerInterface[TRequest, TResponse]):
    """
    Generic implementation of service call management

    Orchestrates the complete workflow for service calls including
    priority queuing, resource allocation, circuit breaker protection,
    and comprehensive monitoring and metrics collection.
    """

    def __init__(
        self,
        config: ServiceConfig,
        queue_manager: QueueManagerInterface[TRequest],
        resource_manager: ResourceManagerInterface,
        external_client: ExternalServiceClientInterface,
        processor: Callable[[TRequest], Awaitable[TResponse]],
    ):
        """
        Initialize call manager with all dependencies

        Args:
            config: Service configuration
            queue_manager: Queue management service for priority handling
            resource_manager: Resource management service for allocation
            external_client: HTTP client for external service communication
            processor: Function to process individual requests
        """
        self.config = config
        self.queue_manager = queue_manager
        self.resource_manager = resource_manager
        self.external_client = external_client
        self.processor = processor

        # Processing state
        self._processing_requests: dict[str, TRequest] = {}
        self._completed_requests: dict[str, TResponse] = {}
        self._processing_lock = asyncio.Lock()

        # Background task for request processing
        self._processor_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()

        # Metrics
        self._start_time = time.time()
        self._total_processed = 0

        logger.debug(f"GenericCallManager initialized for service: {config.name}")

    async def process_request(self, request: TRequest) -> TResponse:
        """
        Process service request through complete workflow

        Args:
            request: Service request with priority and configuration

        Returns:
            Service response containing result and metadata

        Raises:
            RuntimeError: If processing fails
        """
        start_time = time.time()

        try:
            # Enqueue request
            request_id = await self.queue_manager.enqueue_request(
                request, request.priority
            )

            logger.debug(f"Request {request_id} enqueued for processing")

            # Wait for processing completion
            result = await self._wait_for_completion(request_id, request.timeout)

            # Record success metrics
            processing_time = time.time() - start_time
            await self.resource_manager.record_success(processing_time)

            return result

        except Exception as e:
            # Record failure metrics
            await self.resource_manager.record_failure(e)

            error_msg = f"Failed to process request: {e}"
            logger.error(error_msg)

            # Create error response
            error_response = self._create_error_response(
                error_msg,
                time.time() - start_time,
                getattr(request, "request_id", None),
            )
            return error_response

    def _create_error_response(
        self, error_msg: str, processing_time: float, request_id: Optional[str]
    ) -> TResponse:
        """
        Create error response - to be overridden by specific implementations

        Args:
            error_msg: Error message
            processing_time: Time taken for processing
            request_id: Request ID if available

        Returns:
            Error response object
        """
        # This is a generic implementation - specific services should override this
        # For now, we'll create a basic response structure
        response_data = {
            "success": False,
            "message": error_msg,
            "processing_time": processing_time,
            "request_id": request_id,
        }

        # This will need to be properly typed in concrete implementations
        return response_data  # type: ignore

    async def _wait_for_completion(
        self, request_id: str, timeout: Optional[float]
    ) -> TResponse:
        """
        Wait for request processing completion

        Args:
            request_id: ID of request to wait for
            timeout: Maximum time to wait

        Returns:
            Completed response

        Raises:
            asyncio.TimeoutError: If timeout exceeded
            RuntimeError: If processing failed
        """
        timeout = timeout or self.config.timeout
        start_time = time.time()

        while time.time() - start_time < timeout:
            async with self._processing_lock:
                if request_id in self._completed_requests:
                    result = self._completed_requests.pop(request_id)
                    return result

            # Wait a bit before checking again
            await asyncio.sleep(0.1)

        raise asyncio.TimeoutError(f"Request {request_id} timed out after {timeout}s")

    async def _background_processor(self) -> None:
        """
        Background task for processing queued requests
        """
        logger.info(f"Starting background processor for {self.config.name}")

        while not self._shutdown_event.is_set():
            try:
                # Get next request from queue
                request = await self.queue_manager.dequeue_request()

                if request is None:
                    # No requests available, wait a bit
                    await asyncio.sleep(0.1)
                    continue

                # Process request in background
                asyncio.create_task(self._process_single_request(request))

            except Exception as e:
                logger.error(f"Error in background processor: {e}")
                await asyncio.sleep(1.0)  # Wait before retrying

        logger.info(f"Background processor stopped for {self.config.name}")

    async def _process_single_request(self, request: TRequest) -> None:
        """
        Process a single request

        Args:
            request: Request to process
        """
        request_id = getattr(request, "request_id", f"req_{int(time.time() * 1000)}")
        start_time = time.time()

        try:
            # Acquire resource
            if not await self.resource_manager.acquire_resource(request.priority):
                error_msg = "Failed to acquire resource"
                logger.warning(f"Request {request_id}: {error_msg}")

                async with self._processing_lock:
                    self._completed_requests[request_id] = self._create_error_response(
                        error_msg, time.time() - start_time, request_id
                    )
                return

            try:
                # Mark as processing
                async with self._processing_lock:
                    self._processing_requests[request_id] = request

                # Process the request
                response = await self.processor(request)

                # Mark as completed
                async with self._processing_lock:
                    self._processing_requests.pop(request_id, None)
                    self._completed_requests[request_id] = response
                    self._total_processed += 1

                logger.debug(f"Request {request_id} processed successfully")

            finally:
                # Always release resource
                await self.resource_manager.release_resource()

        except Exception as e:
            logger.error(f"Error processing request {request_id}: {e}")

            async with self._processing_lock:
                self._processing_requests.pop(request_id, None)
                self._completed_requests[request_id] = self._create_error_response(
                    str(e), time.time() - start_time, request_id
                )

    async def get_system_status(self) -> dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary containing queue status, resource status, and metrics
        """
        try:
            # Get status from all components
            queue_status = await self.queue_manager.get_queue_status()
            resource_status = await self.resource_manager.get_resource_status()
            performance_metrics = await self.resource_manager.get_performance_metrics()

            async with self._processing_lock:
                processing_count = len(self._processing_requests)

            uptime = time.time() - self._start_time

            return {
                "service_name": self.config.name,
                "uptime_seconds": uptime,
                "queue_status": {
                    "high_priority_count": queue_status.high_priority_count,
                    "medium_priority_count": queue_status.medium_priority_count,
                    "low_priority_count": queue_status.low_priority_count,
                    "total_pending": queue_status.total_pending,
                    "average_wait_time": queue_status.average_wait_time,
                    "processing_count": processing_count,
                },
                "resource_status": {
                    "utilization": resource_status.gpu_utilization,
                    "memory_usage": resource_status.memory_usage,
                    "active_connections": resource_status.active_connections,
                    "max_connections": resource_status.max_connections,
                    "circuit_breaker_state": resource_status.circuit_breaker_state.value,
                    "is_healthy": resource_status.is_healthy,
                },
                "performance_metrics": performance_metrics,
                "total_processed": self._total_processed,
            }

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "service_name": self.config.name,
                "error": str(e),
                "status": "error",
            }

    async def initialize(self) -> None:
        """
        Initialize call manager and all dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info(
                f"Initializing GenericCallManager for service: {self.config.name}"
            )

            # Initialize all components
            await self.queue_manager.initialize()
            await self.resource_manager.initialize()
            await self.external_client.initialize()

            # Start background processor
            self._processor_task = asyncio.create_task(self._background_processor())

            logger.success(
                f"GenericCallManager initialized successfully for service: {self.config.name}"
            )

        except Exception as e:
            error_msg = f"Failed to initialize GenericCallManager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup(self) -> None:
        """
        Cleanup call manager and release all resources
        """
        try:
            logger.info(
                f"Cleaning up GenericCallManager for service: {self.config.name}"
            )

            # Stop background processor
            self._shutdown_event.set()
            if self._processor_task:
                await self._processor_task

            # Cleanup all components
            await self.queue_manager.cleanup()
            await self.resource_manager.cleanup()
            await self.external_client.cleanup()

            # Clear internal state
            async with self._processing_lock:
                self._processing_requests.clear()
                self._completed_requests.clear()

            logger.success(
                f"GenericCallManager cleaned up successfully for service: {self.config.name}"
            )

        except Exception as e:
            logger.error(f"Error during GenericCallManager cleanup: {e}")
            raise
