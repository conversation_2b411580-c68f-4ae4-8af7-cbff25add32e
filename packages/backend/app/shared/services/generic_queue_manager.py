"""
Generic queue manager service implementation

Manages priority queues for service requests, implementing intelligent
scheduling and resource allocation based on request priorities and system load.
"""

import asyncio
import time
from collections import deque
from typing import Optional, TypeVar

from loguru import logger

from app.shared.interfaces.queue_manager_interface import QueueManagerInterface
from app.shared.models.queue_status import QueueStatus
from app.shared.models.queued_request import QueuedRequest
from app.shared.models.request_priority import RequestPriority
from app.shared.models.service_config import ServiceConfig


T = TypeVar("T")


class GenericQueueManager(QueueManagerInterface[T]):
    """
    Generic implementation of priority queue management for service requests

    Provides intelligent scheduling with priority-based queuing, dynamic
    priority adjustment, and comprehensive queue monitoring and metrics.
    """

    def __init__(self, config: ServiceConfig):
        """
        Initialize queue manager with configuration

        Args:
            config: Service configuration containing queue settings
        """
        self.config = config

        # Priority queues using deque for efficient operations
        self._queues: dict[RequestPriority, deque[QueuedRequest[T]]] = {
            RequestPriority.HIGH: deque(),
            RequestPriority.MEDIUM: deque(),
            RequestPriority.LOW: deque(),
        }

        # Request tracking
        self._request_lookup: dict[str, QueuedRequest[T]] = {}
        self._queue_stats = {
            "total_enqueued": 0,
            "total_dequeued": 0,
            "total_cancelled": 0,
        }

        # Queue monitoring
        self._wait_times: dict[RequestPriority, deque[float]] = {
            priority: deque(maxlen=100) for priority in RequestPriority
        }

        # Lock for thread-safe operations
        self._lock = asyncio.Lock()

        logger.debug(f"GenericQueueManager initialized for service: {config.name}")

    async def enqueue_request(self, request: T, priority: RequestPriority) -> str:
        """
        Add request to appropriate priority queue

        Args:
            request: Service request to queue
            priority: Request priority level

        Returns:
            Request ID for tracking

        Raises:
            RuntimeError: If queue is full or system is overloaded
        """
        async with self._lock:
            try:
                # Check queue capacity
                total_pending = sum(len(queue) for queue in self._queues.values())

                if total_pending >= self.config.max_queue_size:
                    error_msg = f"Queue is full (max: {self.config.max_queue_size})"
                    logger.warning(error_msg)
                    raise RuntimeError(error_msg)

                # Generate request ID if not provided
                request_id = (
                    getattr(request, "request_id", None)
                    or f"{self.config.name}_{int(time.time() * 1000)}"
                )

                # Create queued request
                queued_request = QueuedRequest(
                    request=request,
                    enqueue_time=time.time(),
                    priority=priority,
                    request_id=request_id,
                )

                # Add to appropriate queue
                self._queues[priority].append(queued_request)

                # Track request for cancellation support
                self._request_lookup[request_id] = queued_request

                # Update statistics
                self._queue_stats["total_enqueued"] += 1

                logger.debug(
                    f"Request {request_id} enqueued with {priority.value} priority "
                    f"(queue size: {len(self._queues[priority])})"
                )

                return request_id

            except Exception as e:
                logger.error(f"Failed to enqueue request: {e}")
                raise RuntimeError(f"Queue operation failed: {e}") from e

    async def dequeue_request(self) -> Optional[T]:
        """
        Get next request from highest priority queue

        Returns:
            Next request to process or None if queues are empty
        """
        async with self._lock:
            try:
                # Check queues in priority order
                for priority in [
                    RequestPriority.HIGH,
                    RequestPriority.MEDIUM,
                    RequestPriority.LOW,
                ]:
                    queue = self._queues[priority]

                    if queue:
                        queued_request = queue.popleft()

                        # Record wait time for metrics
                        wait_time = queued_request.get_wait_time()
                        self._wait_times[priority].append(wait_time)

                        # Remove from lookup
                        self._request_lookup.pop(queued_request.request_id, None)

                        # Update statistics
                        self._queue_stats["total_dequeued"] += 1

                        logger.debug(
                            f"Request {queued_request.request_id} dequeued from {priority.value} "
                            f"priority (wait time: {wait_time:.2f}s)"
                        )

                        return queued_request.request

                # No requests available
                return None

            except Exception as e:
                logger.error(f"Failed to dequeue request: {e}")
                raise RuntimeError(f"Queue operation failed: {e}") from e

    async def cancel_request(self, request_id: str) -> bool:
        """
        Cancel queued request by ID

        Args:
            request_id: ID of request to cancel

        Returns:
            True if request was cancelled, False if not found
        """
        async with self._lock:
            try:
                if request_id not in self._request_lookup:
                    return False

                queued_request = self._request_lookup[request_id]
                priority = queued_request.priority

                # Remove from queue
                try:
                    self._queues[priority].remove(queued_request)
                    self._request_lookup.pop(request_id)
                    self._queue_stats["total_cancelled"] += 1

                    logger.debug(
                        f"Request {request_id} cancelled from {priority.value} priority"
                    )
                    return True

                except ValueError:
                    # Request not in queue (might have been processed)
                    self._request_lookup.pop(request_id, None)
                    return False

            except Exception as e:
                logger.error(f"Failed to cancel request {request_id}: {e}")
                return False

    async def get_queue_status(self) -> QueueStatus:
        """
        Get current queue status and metrics

        Returns:
            QueueStatus containing queue lengths and wait times
        """
        async with self._lock:
            try:
                # Calculate average wait times
                total_wait_time = 0.0
                total_requests = 0

                for priority in RequestPriority:
                    wait_times = self._wait_times[priority]
                    if wait_times:
                        total_wait_time += sum(wait_times)
                        total_requests += len(wait_times)

                average_wait_time = (
                    total_wait_time / total_requests if total_requests > 0 else 0.0
                )

                return QueueStatus(
                    high_priority_count=len(self._queues[RequestPriority.HIGH]),
                    medium_priority_count=len(self._queues[RequestPriority.MEDIUM]),
                    low_priority_count=len(self._queues[RequestPriority.LOW]),
                    total_pending=sum(len(queue) for queue in self._queues.values()),
                    average_wait_time=average_wait_time,
                    processing_count=0,  # This would be managed by the call manager
                )

            except Exception as e:
                logger.error(f"Failed to get queue status: {e}")
                # Return empty status on error
                return QueueStatus(
                    high_priority_count=0,
                    medium_priority_count=0,
                    low_priority_count=0,
                    total_pending=0,
                    average_wait_time=0.0,
                    processing_count=0,
                )

    async def initialize(self) -> None:
        """
        Initialize queue manager

        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info(
                f"Initializing GenericQueueManager for service: {self.config.name}"
            )
            # Queue manager doesn't need async initialization currently
            logger.success(
                f"GenericQueueManager initialized successfully for service: {self.config.name}"
            )
        except Exception as e:
            error_msg = f"Failed to initialize GenericQueueManager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup(self) -> None:
        """
        Cleanup queue manager and release resources
        """
        try:
            logger.info(
                f"Cleaning up GenericQueueManager for service: {self.config.name}"
            )

            async with self._lock:
                # Clear all queues
                for queue in self._queues.values():
                    queue.clear()

                # Clear tracking data
                self._request_lookup.clear()
                for wait_times in self._wait_times.values():
                    wait_times.clear()

            logger.success(
                f"GenericQueueManager cleaned up successfully for service: {self.config.name}"
            )
        except Exception as e:
            logger.error(f"Error during GenericQueueManager cleanup: {e}")
            raise
