"""
Audio processing request adapter for the audio processor service

Provides clean separation between the API layer and business logic by handling
request parameter conversion, validation, and JSON parsing.
"""

import json
from typing import Optional

from fastapi import HTTPException
from loguru import logger

from models.api.supported_language import SupportedLanguage
from models.config.transcription_config import TranscriptionConfig
from models.services.audio_processing_request import AudioProcessingRequest


class AudioProcessingRequestAdapter:
    """
    Adapter for converting FastAPI request parameters to internal processing models

    Handles parameter validation, JSON parsing, and model conversion for audio
    processing requests.
    """

    @staticmethod
    def convert_transcription_request(
        file_path: str,
        language: Optional[str] = "ar",
        vad_onset: float = 0.5,
        vad_offset: float = 0.363,
        chunk_size: int = 30,
        enable_diarization: bool = False,
        min_speakers: Optional[int] = None,
        max_speakers: Optional[int] = None,
        chapter_markers: Optional[str] = None,
        chapter_timestamps: Optional[str] = None,
        split_chapters: bool = False,
    ) -> AudioProcessingRequest:
        """
        Convert FastAPI request parameters to internal AudioProcessingRequest

        Args:
            file_path: Path to the audio file
            language: Transcription language
            vad_onset: Voice activity detection onset threshold
            vad_offset: Voice activity detection offset threshold
            chunk_size: Processing chunk size in seconds
            enable_diarization: Enable speaker diarization
            min_speakers: Minimum number of speakers
            max_speakers: Maximum number of speakers
            chapter_markers: JSON string of chapter markers list
            chapter_timestamps: JSON string of chapter timestamps list
            split_chapters: Whether to split audio into chapters

        Returns:
            AudioProcessingRequest object

        Raises:
            HTTPException: If parameter validation fails
        """
        try:
            # Parse JSON strings for chapter parameters
            parsed_chapter_markers = None
            parsed_chapter_timestamps = None

            if chapter_markers:
                try:
                    parsed_chapter_markers = json.loads(chapter_markers)
                except json.JSONDecodeError as e:
                    raise HTTPException(
                        status_code=400, detail=f"Invalid JSON in chapter_markers: {e}"
                    ) from e

            if chapter_timestamps:
                try:
                    parsed_chapter_timestamps = json.loads(chapter_timestamps)
                except json.JSONDecodeError as e:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid JSON in chapter_timestamps: {e}",
                    ) from e

            # Convert language string to SupportedLanguage enum
            supported_language = None
            if language:
                try:
                    supported_language = SupportedLanguage(language)
                except ValueError:
                    supported_language = SupportedLanguage.ARABIC  # Default fallback

            # Create transcription configuration
            transcription_config = TranscriptionConfig(
                audio_file=file_path,
                language=supported_language,
                vad_onset=vad_onset,
                vad_offset=vad_offset,
                chunk_size=chunk_size,
                enable_diarization=enable_diarization,
                min_speakers=min_speakers,
                max_speakers=max_speakers,
                chapter_markers=parsed_chapter_markers,
                chapter_timestamps=parsed_chapter_timestamps,
                split_chapters=split_chapters,
            )

            return AudioProcessingRequest(
                audio_file_path=file_path,
                config=transcription_config,
            )

        except HTTPException:
            raise  # Re-raise HTTP exceptions as-is
        except Exception as e:
            error_msg = f"Failed to convert request parameters: {e}"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg) from e
