"""
Audio processing response adapter for the audio processor service

Provides clean separation between the API layer and business logic by handling
response model conversion and formatting for audio processing results.
"""

from loguru import logger

from models.api.transcription_response import TranscriptionResponse
from models.services.audio_processing_response import AudioProcessingResponse


class AudioProcessingResponseAdapter:
    """
    Adapter for converting internal processing results to FastAPI response models

    Handles model conversion and response formatting for audio processing results.
    """

    @staticmethod
    def convert_to_transcription_response(
        processing_response: AudioProcessingResponse,
    ) -> TranscriptionResponse:
        """
        Convert internal AudioProcessingResponse to FastAPI TranscriptionResponse

        Args:
            processing_response: Internal processing response

        Returns:
            TranscriptionResponse for FastAPI endpoint
        """
        try:
            return TranscriptionResponse(
                success=processing_response.success,
                message=processing_response.message,
                segments=processing_response.segments,
                chapters=processing_response.chapters,
                report=processing_response.report,
                language=processing_response.language,
                duration=processing_response.duration,
                processing_time=processing_response.processing_time,
                word_count=processing_response.word_count,
            )
        except Exception as e:
            error_msg = f"Failed to convert processing response: {e}"
            logger.error(error_msg)
            # Return error response
            return TranscriptionResponse(
                success=False,
                message=error_msg,
                segments=[],
                chapters=None,
                report=None,
                language=None,
                duration=None,
                processing_time=None,
                word_count=0,
            )
