"""
Audio transcription API routes

Simplified API layer using clean architecture with dependency injection,
adapter pattern, and proper separation of concerns.
"""

from typing import Optional

from adapters.audio_processing_request_adapter import AudioProcessingRequestAdapter
from adapters.audio_processing_response_adapter import AudioProcessingResponseAdapter
from adapters.file_upload_adapter import FileUploadAdapter
from api.dependencies import get_audio_processing_service
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile
from interfaces import AudioProcessingService
from loguru import logger

from models.api.transcription_response import TranscriptionResponse


router = APIRouter()


@router.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    file: UploadFile = File(..., description="Audio file to transcribe"),
    # Form parameters for multipart/form-data support
    language: Optional[str] = Form("ar", description="Transcription language"),
    vad_onset: float = Form(
        0.5, description="Voice activity detection onset threshold"
    ),
    vad_offset: float = Form(
        0.363, description="Voice activity detection offset threshold"
    ),
    chunk_size: int = Form(30, description="Processing chunk size in seconds"),
    enable_diarization: bool = Form(False, description="Enable speaker diarization"),
    min_speakers: Optional[int] = Form(None, description="Minimum number of speakers"),
    max_speakers: Optional[int] = Form(None, description="Maximum number of speakers"),
    chapter_markers: Optional[str] = Form(
        None, description="JSON string of chapter markers list"
    ),
    chapter_timestamps: Optional[str] = Form(
        None, description="JSON string of chapter timestamps list"
    ),
    split_chapters: bool = Form(
        False, description="Whether to split audio into chapters"
    ),
    audio_service: AudioProcessingService = Depends(get_audio_processing_service),
) -> TranscriptionResponse:
    """
    Arabic speech-to-text endpoint: Accepts audio files and returns recognized text with
    timestamps

    Features:
    - Uses WhisperX model specialized for Arabic speech recognition
    - Automatically generates text content and timestamps
    - Supports speaker diarization (optional)
    - Supports chapter detection (optional):
      - Mode 1: No chapter detection (default)
      - Mode 2: Timestamp-based splitting (provide chapter_timestamps)
      - Mode 3: Marker-based detection (provide chapter_markers)
    - Supports splitting audio into chapters (when split_chapters=True):
      - Returns base64-encoded audio data for each chapter
      - Returns corresponding text content for each chapter
    """
    temp_file_path = None

    try:
        logger.info(f"Received transcription request for file: {file.filename}")

        # Save uploaded file using adapter
        temp_file_path = await FileUploadAdapter.save_uploaded_file(file)

        # Convert API request to internal model using adapter
        processing_request = (
            AudioProcessingRequestAdapter.convert_transcription_request(
                file_path=str(temp_file_path),
                language=language,
                vad_onset=vad_onset,
                vad_offset=vad_offset,
                chunk_size=chunk_size,
                enable_diarization=enable_diarization,
                min_speakers=min_speakers,
                max_speakers=max_speakers,
                chapter_markers=chapter_markers,
                chapter_timestamps=chapter_timestamps,
                split_chapters=split_chapters,
            )
        )

        # Process audio using the orchestration service
        processing_response = await audio_service.process_audio(processing_request)

        # Convert internal response to API response using adapter
        api_response = AudioProcessingResponseAdapter.convert_to_transcription_response(
            processing_response
        )

        logger.success(f"Transcription completed successfully for: {file.filename}")
        return api_response

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        error_msg = f"Transcription failed: {e!s}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

    finally:
        # Clean up temporary file using adapter
        FileUploadAdapter.cleanup_temp_file(temp_file_path)
