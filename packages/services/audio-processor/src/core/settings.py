"""
Audio processing service configuration

Each microservice creates a separate configuration file to ensure service
independence and maintainability.

Configuration Philosophy:
- Default values in this file provide sensible, production-ready settings
- Environment variables (.env) should only contain sensitive information
- Non-sensitive configuration can be overridden via environment variables if needed
- This approach keeps .env files minimal and focused on secrets
"""

from pathlib import Path
from typing import Optional

from loguru import logger
from pydantic import ValidationInfo, field_validator
from pydantic_settings import BaseSettings

from .model_validator import ModelValidator


# pydantic.BaseSettings is a class in the Pydantic library for defining
# and validating configurations.
# It provides many useful features, such as:
# 1. Automatically load configuration from environment variables, config files,
# or command line arguments
# 2. Provide type checking and validation
# 3. Support configuration inheritance and overrides
# 4. Support default values and optional values for configuration
# 5. Support configuration docstrings and metadata
class Settings(BaseSettings):
    """Application configuration"""

    # Service root directory (audio-processor service root)
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent

    # Distributed model configuration - models stored locally in service assets
    MODEL_PATH: str = "./assets/transcription/whisper/faster-whisper-large-v3-turbo-ct2"
    ALIGN_MODEL_PATH: str = (
        "./assets/alignment/arabic/jonatasgrosman-wav2vec2-large-xlsr-53-arabic"
    )
    DEVICE: str = "cuda"  # cuda (recommended for performance) or cpu (fallback)
    COMPUTE_TYPE: str = "float16"  # float16 (GPU), int8 (CPU/GPU), float32 (CPU)

    # Service configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8001
    DEBUG: bool = True

    # Audio processing configuration
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    SUPPORTED_FORMATS: list = ["wav", "mp3", "m4a", "flac", "ogg"]
    DEFAULT_LANGUAGE: str = "ar"

    # VAD default configuration
    DEFAULT_VAD_ONSET: float = 0.5
    DEFAULT_VAD_OFFSET: float = 0.363
    DEFAULT_CHUNK_SIZE: int = 30

    # Speaker diarization configuration
    HF_TOKEN: Optional[str] = None  # HuggingFace Token for diarization
    DEFAULT_ENABLE_DIARIZATION: bool = False
    DEFAULT_MIN_SPEAKERS: Optional[int] = None
    DEFAULT_MAX_SPEAKERS: Optional[int] = None

    # CUDA optimization configuration
    ENABLE_TF32: bool = True  # Enable TF32 for RTX 30/40 series performance
    ENABLE_CUDNN_BENCHMARK: bool = True  # Enable cuDNN benchmark mode
    ENABLE_CUDNN_DETERMINISTIC: bool = False  # Enable for reproducible results

    # Logging configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"

    # Chapter detection configuration
    CHAPTER_MIN_MATCH_WORDS: int = (
        2  # Minimum words required for cross-segment matching trigger
    )
    CHAPTER_CONTEXT_SEGMENTS: int = (
        4  # Number of segments before/after target for semantic validation context
        # Token usage with thinking mode: ~2380 tokens max (safe within 4096 limit)
        # Provides richer context for better semantic understanding
        # Can be adjusted: 2=conservative, 4=balanced, 6=maximum (risky)
    )
    CHAPTER_BATCH_SIZE: int = 90  # Batch size for camel-tools API calls (must be < 100)

    # API timeout configuration
    CAMEL_TOOLS_TIMEOUT: float = 30.0  # Timeout for camel-tools API calls
    QWEN_SERVICE_TIMEOUT: float = 30.0  # Timeout for qwen-service API calls
    BATCH_PROCESSING_TIMEOUT: float = 60.0  # Timeout for batch processing operations

    # Note: Enhanced text normalization (remove_punctuation, normalize_digits,
    # normalize_whitespace, normalize_english_case) is now mandatory in camel-tools-api

    # Enhanced semantic validation configuration for structural marker detection
    # Based on multi-criteria analysis approach that focuses on structural function
    # rather than traditional title formatting - proven effective in testing
    CHAPTER_DETECTION_PROMPT_TEMPLATE: str = (
        "هل '{title}' علامة هيكلية تشير إلى بداية قسم جديد في النص التالي؟\n\n"
        "النص: {context}\n\n"
        "احلل:\n"
        "1. هل يوجد تغيير موضوعي قبل وبعد '{title}'؟\n"
        "2. هل تعمل '{title}' كنقطة انتقال بين موضوعين؟\n"
        "3. هل '{title}' بداية لقسم جديد؟\n\n"
        "أجب: نعم أو لا"
    )
    CHAPTER_DETECTION_SYSTEM_PROMPT: str = (
        "أنت خبير في تحليل النصوص العربية وتحديد العلامات الهيكلية. "
        "العلامات الهيكلية تشير إلى انتقالات موضوعية أو بداية أقسام جديدة، "
        "حتى لو لم تكن مُنسقة كعناوين تقليدية. "
        "أجب بكلمة واحدة فقط: نعم أو لا."
    )

    # AI model parameters for chapter detection
    QWEN_ENABLE_THINKING: bool = True  # Enable thinking mode for better analysis
    # Note: Temperature and TopP are handled by qwen-service defaults
    # For thinking mode: Temperature=0.6, TopP=0.95 (as per official docs)

    # External service configuration
    CAMEL_TOOLS_API_URL: str = "http://localhost:8002"
    # Updated to use backend call manager instead of direct qwen-service calls
    QWEN_SERVICE_API_URL: str = "http://localhost:8000/api/v1/qwen"

    # Service lifecycle configuration
    ENABLE_SERVICE_CACHING: bool = True
    SERVICE_INITIALIZATION_TIMEOUT: float = 60.0
    SERVICE_CLEANUP_TIMEOUT: float = 30.0

    # HTTP client configuration
    HTTP_CLIENT_TIMEOUT: float = 30.0
    HTTP_CLIENT_MAX_RETRIES: int = 3
    HTTP_CLIENT_RETRY_DELAY: float = 1.0

    # The following logic will be automatically executed by pydantic
    # when the class is instantiated
    @field_validator("MODEL_PATH")
    @classmethod
    # `v` is the value being validated by the following logic, here is the model path
    def validate_model_path(cls, v: str, info: ValidationInfo) -> str:
        """
        Validate that model path exists and contains necessary files

        实际的数据结构如下
        info = ValidationInfo(
            data: dict[str, Any] = {
                'BASE_DIR': PosixPath('/home/<USER>/projects/website_project/packages/services/audio-processor'),
                'Host': '0.0.0.0',
                'Port': 8001,
            }, 这里存储的是已经经过验证的字段值

            field_name: str ='MODEL_PATH', 这里存储的是当前正在验证的字段名
            config: dict = ConfigObj()
        )

        pydantic 验证字段的顺序是不固定的, 可能出现先验证MODEL_PATH 再验证BASE_DIR的情况
        所以代码做了防御性编程处理

        """  # noqa: E501
        if "BASE_DIR" not in info.data:
            # Fallback to service root directory
            base_dir = Path(__file__).resolve().parent.parent.parent
        else:
            base_dir = info.data["BASE_DIR"]

        # If model path is relative, make it relative to service root directory
        model_path = Path(v) if Path(v).is_absolute() else base_dir / v

        if not model_path.exists():
            msg = f"Model directory does not exist: {model_path}"
            raise ValueError(msg)
        if not model_path.is_dir():
            msg = f"Model path is not a directory: {model_path}"
            raise ValueError(msg)

        # Use ModelValidator for validation
        validator = ModelValidator()
        is_valid, missing_files = validator.validate_whisper_model(model_path)

        if not is_valid:
            msg = (
                f"Missing files in model directory {model_path}: "
                f"{', '.join(missing_files)}"
            )
            raise ValueError(msg)

        return str(model_path.resolve())

    @field_validator("ALIGN_MODEL_PATH")
    @classmethod
    def validate_align_model_path(cls, v: str, info: ValidationInfo) -> str:
        """Validate that alignment model path exists (optional validation)"""
        if "BASE_DIR" not in info.data:
            # Fallback to service root directory
            base_dir = Path(__file__).resolve().parent.parent.parent
        else:
            base_dir = info.data["BASE_DIR"]

        # If model path is relative, make it relative to service root directory
        align_model_path = Path(v) if Path(v).is_absolute() else base_dir / v

        # For alignment model, we allow it to not exist initially
        # It will be downloaded automatically if needed
        if align_model_path.exists():
            if not align_model_path.is_dir():
                msg = f"Alignment model path is not a directory: {align_model_path}"
                raise ValueError(msg)

            # Use ModelValidator for validation
            validator = ModelValidator()
            is_valid, missing_files = validator.validate_alignment_model(
                align_model_path
            )

            if is_valid:
                logger.info(f"Valid alignment model found in {align_model_path}")
            else:
                logger.warning(
                    f"Alignment model directory exists but missing files: "
                    f"{missing_files} in {align_model_path}. "
                    f"Model will be downloaded if needed."
                )
        else:
            logger.info(
                f"Alignment model directory does not exist: {align_model_path}. "
                f"Model will be downloaded automatically when needed."
            )

        return str(align_model_path.resolve())

    @field_validator("CAMEL_TOOLS_API_URL")
    @classmethod
    def validate_camel_tools_url(cls, v: str) -> str:
        """Validate camel-tools-api URL format"""
        if not v.startswith(("http://", "https://")):
            msg = "CAMEL_TOOLS_API_URL must start with http:// or https://"
            raise ValueError(msg)
        return v.rstrip("/")  # Remove trailing slash for consistency

    @field_validator("QWEN_SERVICE_API_URL")
    @classmethod
    def validate_qwen_service_url(cls, v: str) -> str:
        """Validate qwen-service URL format"""
        if not v.startswith(("http://", "https://")):
            msg = "QWEN_SERVICE_API_URL must start with http:// or https://"
            raise ValueError(msg)
        return v.rstrip("/")  # Remove trailing slash for consistency

    @field_validator(
        "SERVICE_INITIALIZATION_TIMEOUT",
        "SERVICE_CLEANUP_TIMEOUT",
        "HTTP_CLIENT_TIMEOUT",
        "HTTP_CLIENT_RETRY_DELAY",
    )
    @classmethod
    def validate_positive_timeout(cls, v: float) -> float:
        """Validate that timeout values are positive"""
        if v <= 0:
            msg = "Timeout values must be positive"
            raise ValueError(msg)
        return v

    @field_validator("HTTP_CLIENT_MAX_RETRIES")
    @classmethod
    def validate_max_retries(cls, v: int) -> int:
        """Validate that max retries is non-negative"""
        if v < 0:
            msg = "HTTP_CLIENT_MAX_RETRIES must be non-negative"
            raise ValueError(msg)
        return v

    class Config:
        # In Pydantic V2+, the env_file path is relative to the current working dir
        # We build an absolute path here to ensure it can always be found
        env_file = str(Path(__file__).resolve().parent.parent.parent / ".env")
        case_sensitive = True
        extra = "ignore"
