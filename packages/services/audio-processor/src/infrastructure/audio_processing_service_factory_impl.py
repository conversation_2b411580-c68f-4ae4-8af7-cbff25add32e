"""
Service factory implementation

Creates and manages audio processing services with proper dependency injection,
following the clean architecture pattern and ensuring proper lifecycle management.
"""

from typing import Optional

from core.config import Settings
from interfaces import (
    AudioProcessingService,
    AudioTranscriptionService,
    ChapterDetectionService,
    ChapterSplittingService,
    ExternalServiceClients,
)
from loguru import logger
from services.chapter_detection import (
    ChapterDetectionOrchestratorImpl,
    CrossSegmentMatchingServiceImpl,
    SegmentReorganizationServiceImpl,
    TextPreprocessingServiceImpl,
    WordLevelTimestampServiceImpl,
)
from services.chapter_processing import ChapterSplittingServiceImpl
from services.orchestration import AudioProcessingServiceImpl
from services.transcription import AudioTranscriptionServiceImpl

from .external_service_clients_impl import ExternalServiceClientsImpl


class AudioProcessingServiceFactoryImpl:
    """
    Implementation of audio processing service factory

    Creates and manages service instances with proper dependency injection
    and lifecycle management for the audio processing service.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the service factory

        Args:
            settings: Application settings for service configuration
        """
        self.settings = settings
        self._external_clients: Optional[ExternalServiceClients] = None
        self._initialized = False

    def create_transcription_service(self) -> AudioTranscriptionService:
        """
        Create audio transcription service instance

        Returns:
            Configured AudioTranscriptionService instance

        Raises:
            RuntimeError: If service creation fails
        """
        try:
            service = AudioTranscriptionServiceImpl(self.settings)
            logger.debug("Created AudioTranscriptionService instance")
            return service
        except Exception as e:
            logger.error(f"Failed to create AudioTranscriptionService: {e}")
            error_msg = "AudioTranscriptionService creation failed"
            raise RuntimeError(error_msg) from e

    def create_chapter_detection_service(self) -> ChapterDetectionService:
        """
        Create chapter detection service instance with full clean architecture

        Returns:
            Configured ChapterDetectionService instance

        Raises:
            RuntimeError: If service creation fails
        """
        try:
            external_clients = self.get_external_clients()

            # Create all specialized services
            text_preprocessing = TextPreprocessingServiceImpl(
                self.settings, external_clients
            )
            cross_segment_matching = CrossSegmentMatchingServiceImpl(
                self.settings, text_preprocessing
            )
            word_level_timestamp = WordLevelTimestampServiceImpl(self.settings)
            segment_reorganization = SegmentReorganizationServiceImpl(
                self.settings, word_level_timestamp
            )

            # Create the orchestrator service with all dependencies
            service = ChapterDetectionOrchestratorImpl(
                self.settings,
                external_clients,
                text_preprocessing,
                cross_segment_matching,
                word_level_timestamp,
                segment_reorganization,
            )
            logger.debug(
                "Created ChapterDetectionService instance with clean architecture"
            )
            return service
        except Exception as e:
            logger.error(f"Failed to create ChapterDetectionService: {e}")
            error_msg = "ChapterDetectionService creation failed"
            raise RuntimeError(error_msg) from e

    def create_chapter_splitting_service(self) -> ChapterSplittingService:
        """
        Create chapter splitting service instance

        Returns:
            Configured ChapterSplittingService instance

        Raises:
            RuntimeError: If service creation fails
        """
        try:
            service = ChapterSplittingServiceImpl(self.settings)
            logger.debug("Created ChapterSplittingService instance")
            return service
        except Exception as e:
            logger.error(f"Failed to create ChapterSplittingService: {e}")
            error_msg = "ChapterSplittingService creation failed"
            raise RuntimeError(error_msg) from e

    def create_audio_processing_service(self) -> AudioProcessingService:
        """
        Create main audio processing orchestration service

        Returns:
            Configured AudioProcessingService instance

        Raises:
            RuntimeError: If service creation fails
        """
        try:
            transcription_service = self.create_transcription_service()
            chapter_detection_service = self.create_chapter_detection_service()
            chapter_splitting_service = self.create_chapter_splitting_service()

            service = AudioProcessingServiceImpl(
                settings=self.settings,
                transcription_service=transcription_service,
                chapter_detection_service=chapter_detection_service,
                chapter_splitting_service=chapter_splitting_service,
            )
            logger.debug("Created AudioProcessingService instance")
            return service
        except Exception as e:
            logger.error(f"Failed to create AudioProcessingService: {e}")
            error_msg = "AudioProcessingService creation failed"
            raise RuntimeError(error_msg) from e

    def get_external_clients(self) -> ExternalServiceClients:
        """
        Get external service clients manager

        Returns:
            Configured ExternalServiceClients instance
        """
        if self._external_clients is None:
            self._external_clients = ExternalServiceClientsImpl(self.settings)
            logger.debug("Created ExternalServiceClients instance")

        return self._external_clients

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization

        Raises:
            RuntimeError: If initialization fails
        """
        if self._initialized:
            return

        try:
            logger.info("Initializing audio processing service factory...")

            # Initialize external service clients
            external_clients = self.get_external_clients()
            await external_clients.initialize()

            self._initialized = True
            logger.success("Audio processing service factory initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize service factory: {e}")
            error_msg = "Service factory initialization failed"
            raise RuntimeError(error_msg) from e

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up audio processing service factory...")

            # Cleanup external service clients
            if self._external_clients:
                await self._external_clients.cleanup()
                self._external_clients = None

            self._initialized = False
            logger.success("Audio processing service factory cleanup completed")

        except Exception as e:
            logger.error(f"Error during service factory cleanup: {e}")
            # Continue with cleanup even if some operations fail

    def is_initialized(self) -> bool:
        """
        Check if factory services are initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized
