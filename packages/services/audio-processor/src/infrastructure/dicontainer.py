"""
Dependency injection container implementation

Manages service lifecycle and provides dependency injection capabilities
for the audio processing service, following the clean architecture pattern.
"""

from typing import Any, Optional, TypeVar, cast

from interfaces import (
    AudioProcessingService,
    AudioProcessingServiceFactory,
    AudioTranscriptionService,
    ChapterDetectionService,
    ChapterSplittingService,
    ExternalServiceClients,
)
from loguru import logger


T = TypeVar("T")


class DIContainer:
    """
    Dependency injection container for audio processing services

    Manages service instances and their lifecycle, providing a centralized
    way to resolve dependencies throughout the application.
    """

    def __init__(self) -> None:
        """Initialize the DI container"""
        self._services: dict[type, Any] = {}
        self._factory: Optional[AudioProcessingServiceFactory] = None
        self._initialized = False

    def register_factory(self, factory: AudioProcessingServiceFactory) -> None:
        """
        Register the service factory

        Args:
            factory: Service factory instance
        """
        self._factory = factory
        logger.debug("Audio processing service factory registered")

    def get_service(self, service_type: type[T]) -> T:
        """
        Get a service instance by type

        Args:
            service_type: Type of service to retrieve

        Returns:
            Service instance

        Raises:
            ValueError: If service factory is not registered
            RuntimeError: If service cannot be created
        """
        if self._factory is None:
            error_msg = "Service factory not registered"
            raise ValueError(error_msg)

        # Check if service is already cached
        if service_type in self._services:
            return cast(T, self._services[service_type])

        # Create new service instance
        try:
            service = self._create_service(service_type)
            self._services[service_type] = service
            logger.debug(f"Created service: {service_type.__name__}")
            return service
        except Exception as e:
            logger.error(f"Failed to create service {service_type.__name__}: {e}")
            error_msg = f"Cannot create service {service_type.__name__}"
            raise RuntimeError(error_msg) from e

    def _create_service(self, service_type: type[T]) -> T:
        """
        Create a service instance using the factory

        Args:
            service_type: Type of service to create

        Returns:
            Created service instance

        Raises:
            ValueError: If service type is not supported or factory is not registered
        """
        if self._factory is None:
            error_msg = "Service factory not registered"
            raise ValueError(error_msg)

        if service_type == AudioTranscriptionService:
            return cast(T, self._factory.create_transcription_service())
        if service_type == ChapterDetectionService:
            return cast(T, self._factory.create_chapter_detection_service())
        if service_type == ChapterSplittingService:
            return cast(T, self._factory.create_chapter_splitting_service())
        if service_type == AudioProcessingService:
            return cast(T, self._factory.create_audio_processing_service())
        if service_type == ExternalServiceClients:
            return cast(T, self._factory.get_external_clients())
        error_msg = f"Unsupported service type: {service_type}"
        raise ValueError(error_msg)

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization
        """
        if self._initialized:
            return

        if self._factory is None:
            error_msg = "Service factory not registered"
            raise ValueError(error_msg)

        try:
            logger.info("Initializing audio processing DI container services...")

            # Initialize the factory's services
            if hasattr(self._factory, "initialize_services"):
                await self._factory.initialize_services()

            self._initialized = True
            logger.success(
                "Audio processing DI container services initialized successfully"
            )

        except Exception as e:
            logger.error(f"Failed to initialize DI container services: {e}")
            raise

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up audio processing DI container services...")

            # Cleanup the factory's services
            if self._factory and hasattr(self._factory, "cleanup_services"):
                await self._factory.cleanup_services()

            # Clear service cache
            self._services.clear()
            self._initialized = False

            logger.success("Audio processing DI container cleanup completed")

        except Exception as e:
            logger.error(f"Error during DI container cleanup: {e}")
            # Continue with cleanup even if some operations fail

    def is_initialized(self) -> bool:
        """
        Check if the container is initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized

    def get_registered_services(self) -> list[str]:
        """
        Get list of registered service types

        Returns:
            List of service type names
        """
        return [service_type.__name__ for service_type in self._services]
