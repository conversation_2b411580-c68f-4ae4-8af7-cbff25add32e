"""
Qwen service client implementation

Provides HTTP client for integrating with qwen-service,
implementing proper error handling, retries, and timeout management.
"""

from typing import Optional

import httpx
from loguru import logger

from core.config import Settings
from interfaces import QwenServiceClient
from models.services.semantic_validation_request import SemanticValidationRequest
from models.services.semantic_validation_response import SemanticValidationResponse


class QwenServiceClientImpl(QwenServiceClient):
    """Implementation of qwen-service client"""

    def __init__(self, settings: Settings):
        """
        Initialize qwen-service client

        Args:
            settings: Application settings containing API configuration
        """
        self.settings = settings
        self.base_url = settings.QWEN_SERVICE_API_URL
        self.timeout = settings.QWEN_SERVICE_TIMEOUT

    async def validate_semantically(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Perform semantic validation using Qwen model

        Args:
            request: Semantic validation request with prompt and configuration

        Returns:
            SemanticValidationResponse containing model response

        Raises:
            RuntimeError: If API call fails
        """
        # Updated to use backend call manager's validate endpoint
        url = f"{self.base_url}/validate"
        payload = {
            "prompt": request.prompt,
            "system_prompt": request.system_prompt,
            "enable_thinking": request.enable_thinking,
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    timeout=self.timeout,
                )
                response.raise_for_status()
                result = response.json()

                return SemanticValidationResponse(
                    success=True,
                    message="Semantic validation completed",
                    generated_text=result.get("generated_text", ""),
                    processing_time=result.get("processing_time"),
                )
        except Exception as e:
            logger.error(f"Error calling qwen-service generate endpoint: {e}")
            error_msg = f"Qwen service API call failed: {e}"
            raise RuntimeError(error_msg) from e

    async def translate_text(
        self, prompt: str, system_prompt: Optional[str] = None
    ) -> SemanticValidationResponse:
        """
        Translate text using Qwen model via backend service

        Args:
            prompt: Text to translate with context
            system_prompt: Optional system prompt for translation context

        Returns:
            SemanticValidationResponse containing translated text

        Raises:
            RuntimeError: If API call fails
        """
        # Use backend service's generate endpoint for translation
        url = f"{self.base_url}/api/v1/qwen/generate"
        payload = {
            "prompt": prompt,
            "system_prompt": system_prompt
            or (
                "You are a professional translator. Translate the given Arabic "
                "text to English while preserving the meaning and context."
            ),
            "enable_thinking": True,
            "source_service": "audio-processor",
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    timeout=self.timeout,
                )
                response.raise_for_status()
                result = response.json()

                return SemanticValidationResponse(
                    success=result.get("success", False),
                    message=result.get("message", "Translation completed"),
                    generated_text=result.get("generated_text", ""),
                    processing_time=result.get("processing_time"),
                )
        except Exception as e:
            logger.error(f"Error calling backend translation endpoint: {e}")
            error_msg = f"Backend translation API call failed: {e}"
            raise RuntimeError(error_msg) from e

    async def health_check(self) -> bool:
        """
        Check if qwen-service is available

        Returns:
            True if service is healthy, False otherwise
        """
        try:
            async with httpx.AsyncClient() as client:
                # Updated to use backend call manager's health endpoint
                response = await client.get(
                    f"{self.base_url}/health",
                    timeout=5.0,  # Short timeout for health checks
                )
                if response.status_code == 200:
                    # Check if response indicates healthy service
                    result = response.json()
                    return result.get("healthy", False)
                return False
        except Exception as e:
            logger.warning(f"Qwen-service health check failed: {e}")
            return False
