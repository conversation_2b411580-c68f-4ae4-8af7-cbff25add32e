"""
Audio processor service interfaces

This module defines the core interfaces and protocols for the audio processing service,
enabling clean separation of concerns and dependency injection.
"""

from .audio_processing_service import AudioProcessingService
from .audio_processing_service_factory import (
    AudioProcessingServiceFactory,
)
from .audio_transcription_service import AudioTranscriptionService
from .camel_tools_client import CamelToolsClient
from .chapter_detection_service import ChapterDetectionService
from .chapter_splitting_service import ChapterSplittingService
from .cross_segment_matching_service import CrossSegmentMatchingService
from .external_service_clients import ExternalServiceClients
from .qwen_service_client import QwenServiceClient
from .segment_reorganization_service import SegmentReorganizationService
from .text_preprocessing_service import TextPreprocessingService
from .word_level_timestamp_service import WordLevelTimestampService


__all__ = [
    "AudioProcessingService",
    # Service factory interfaces
    "AudioProcessingServiceFactory",
    # Core audio processing interfaces
    "AudioTranscriptionService",
    "ChapterDetectionService",
    "ChapterSplittingService",
    # Chapter detection service interfaces
    "CrossSegmentMatchingService",
    "SegmentReorganizationService",
    "TextPreprocessingService",
    "WordLevelTimestampService",
    # External service interfaces
    "CamelToolsClient",
    "ExternalServiceClients",
    "QwenServiceClient",
]
