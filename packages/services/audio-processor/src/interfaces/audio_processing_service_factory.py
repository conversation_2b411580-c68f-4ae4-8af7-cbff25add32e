"""
Service factory interfaces

These protocols define the contracts for service creation and lifecycle management,
enabling dependency injection and proper resource management throughout the application.
"""

from typing import Protocol, runtime_checkable

from .audio_processing_service import AudioProcessingService
from .audio_transcription_service import AudioTranscriptionService
from .chapter_detection_service import ChapterDetectionService
from .chapter_splitting_service import ChapterSplittingService
from .external_service_clients import ExternalServiceClients


@runtime_checkable
class AudioProcessingServiceFactory(Protocol):
    """
    Protocol for creating audio processing services with proper dependency injection
    """

    def create_transcription_service(self) -> AudioTranscriptionService:
        """
        Create audio transcription service instance

        Returns:
            Configured AudioTranscriptionService instance

        Raises:
            RuntimeError: If service creation fails
        """
        ...

    def create_chapter_detection_service(self) -> ChapterDetectionService:
        """
        Create chapter detection service instance

        Returns:
            Configured ChapterDetectionService instance

        Raises:
            RuntimeError: If service creation fails
        """
        ...

    def create_chapter_splitting_service(self) -> ChapterSplittingService:
        """
        Create chapter splitting service instance

        Returns:
            Configured ChapterSplittingService instance

        Raises:
            RuntimeError: If service creation fails
        """
        ...

    def create_audio_processing_service(self) -> AudioProcessingService:
        """
        Create main audio processing orchestration service

        Returns:
            Configured AudioProcessingService instance

        Raises:
            RuntimeError: If service creation fails
        """
        ...

    def get_external_clients(self) -> ExternalServiceClients:
        """
        Get external service clients manager

        Returns:
            Configured ExternalServiceClients instance
        """
        ...

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        ...

    def is_initialized(self) -> bool:
        """
        Check if factory services are initialized

        Returns:
            True if initialized, False otherwise
        """
        ...
