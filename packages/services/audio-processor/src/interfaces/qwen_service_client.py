"""
Qwen service client interface

Defines the protocol for qwen-service integration,
providing standardized semantic validation capabilities.
"""

from typing import Optional, Protocol, runtime_checkable

from models.services.semantic_validation_request import SemanticValidationRequest
from models.services.semantic_validation_response import SemanticValidationResponse


@runtime_checkable
class QwenServiceClient(Protocol):
    """Protocol for qwen-service integration"""

    async def validate_semantically(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Perform semantic validation using Qwen model

        Args:
            request: Semantic validation request with prompt and configuration

        Returns:
            SemanticValidationResponse containing model response

        Raises:
            RuntimeError: If API call fails
        """
        ...

    async def translate_text(
        self, prompt: str, system_prompt: Optional[str] = None
    ) -> SemanticValidationResponse:
        """
        Translate text using Qwen model via backend service

        Args:
            prompt: Text to translate with context
            system_prompt: Optional system prompt for translation context

        Returns:
            SemanticValidationResponse containing translated text

        Raises:
            RuntimeError: If API call fails
        """
        ...

    async def health_check(self) -> bool:
        """
        Check if qwen-service is available

        Returns:
            True if service is healthy, False otherwise
        """
        ...
