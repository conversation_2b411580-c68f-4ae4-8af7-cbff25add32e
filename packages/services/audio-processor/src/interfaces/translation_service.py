"""
Translation service interface

Defines the protocol for translation functionality,
providing context-aware translation of transcription segments.
"""

from typing import Protocol, runtime_checkable

from models.domain.transcription_segment import TranscriptionSegment


@runtime_checkable
class TranslationService(Protocol):
    """Protocol for translation service"""

    async def translate_segments(
        self, segments: list[TranscriptionSegment]
    ) -> list[TranscriptionSegment]:
        """
        Translate transcription segments with context awareness

        For each segment, creates context by including 4 previous and 4 next segments,
        then sends the contextualized text to the translation model.

        Args:
            segments: List of transcription segments to translate

        Returns:
            List of transcription segments with translated_text field populated

        Raises:
            RuntimeError: If translation fails
        """
        ...
