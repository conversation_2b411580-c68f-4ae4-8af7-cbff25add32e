"""
Transcription response model for the audio processor service

Contains the TranscriptionResponse Pydantic model that defines
the external API response interface for transcription operations.
"""

from typing import Optional

from pydantic import BaseModel, Field

# Direct imports to avoid forward reference issues
from models.domain.chapter import Chapter
from models.domain.transcription_segment import TranscriptionSegment

from .process_report_entry import ProcessReportEntry


class TranscriptionResponse(BaseModel):
    """
    Transcription response model

    Contains detailed transcription results and metadata information.
    """

    success: bool = Field(description="Whether transcription was successful")
    message: str = Field(description="Response message")

    segments: Optional[list[TranscriptionSegment]] = Field(
        default=None,
        description="List of transcription segments with timestamps and text",
    )

    chapters: Optional[list[Chapter]] = Field(
        default=None,
        description="List of detected chapters with timestamps and titles",
    )

    report: Optional[list[ProcessReportEntry]] = Field(
        default=None,
        description="Detailed report of the chapter detection and splitting process.",
    )

    language: Optional[str] = Field(
        default=None, description="Detected or specified language code"
    )

    duration: Optional[float] = Field(
        default=None, description="Total audio duration (seconds)"
    )

    processing_time: Optional[float] = Field(
        default=None, description="Processing time (seconds)"
    )

    word_count: Optional[int] = Field(
        default=None, description="Total number of transcribed words"
    )
