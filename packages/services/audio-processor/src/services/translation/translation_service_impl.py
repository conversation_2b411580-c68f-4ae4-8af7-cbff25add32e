"""
Translation service implementation

Provides context-aware translation of transcription segments using
the Qwen model via backend service integration.
"""

from interfaces.qwen_service_client import QwenServiceClient
from interfaces.translation_service import TranslationService
from loguru import logger
from models.domain.transcription_segment import TranscriptionSegment


class TranslationServiceImpl(TranslationService):
    """Implementation of translation service with context awareness"""

    def __init__(self, qwen_client: QwenServiceClient):
        """
        Initialize translation service

        Args:
            qwen_client: Client for communicating with backend Qwen service
        """
        self.qwen_client = qwen_client

    async def translate_segments(
        self, segments: list[TranscriptionSegment]
    ) -> list[TranscriptionSegment]:
        """
        Translate transcription segments with context awareness

        For each segment, creates context by including 4 previous and 4 next segments,
        then sends the contextualized text to the translation model.

        Args:
            segments: List of transcription segments to translate

        Returns:
            List of transcription segments with translated_text field populated

        Raises:
            RuntimeError: If translation fails
        """
        if not segments:
            logger.info("No segments to translate")
            return segments

        logger.info(f"Starting translation of {len(segments)} segments")
        translated_segments = []

        for i, segment in enumerate(segments):
            try:
                # Build context with 4 previous and 4 next segments
                context_text = self._build_context(segments, i)
                
                # Create translation prompt
                translation_prompt = self._create_translation_prompt(
                    segment.text, context_text
                )
                
                # Call translation service
                translation_result = await self.qwen_client.translate_text(
                    prompt=translation_prompt
                )
                
                if translation_result.success:
                    # Create new segment with translation
                    translated_segment = TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=segment.text,
                        speaker=segment.speaker,
                        words=segment.words,
                        translated_text=translation_result.generated_text.strip(),
                    )
                    translated_segments.append(translated_segment)
                    logger.debug(f"Successfully translated segment {i+1}/{len(segments)}")
                else:
                    # Keep original segment without translation on failure
                    logger.warning(
                        f"Translation failed for segment {i+1}: {translation_result.message}"
                    )
                    translated_segments.append(segment)
                    
            except Exception as e:
                logger.error(f"Error translating segment {i+1}: {e}")
                # Keep original segment without translation on error
                translated_segments.append(segment)

        logger.info(f"Translation completed for {len(translated_segments)} segments")
        return translated_segments

    def _build_context(self, segments: list[TranscriptionSegment], current_index: int) -> str:
        """
        Build context text with 4 previous and 4 next segments

        Args:
            segments: All transcription segments
            current_index: Index of current segment being translated

        Returns:
            Context text containing surrounding segments
        """
        context_segments = []
        
        # Add 4 previous segments
        start_idx = max(0, current_index - 4)
        for i in range(start_idx, current_index):
            context_segments.append(f"[Previous {current_index - i}]: {segments[i].text}")
        
        # Add current segment
        context_segments.append(f"[Current]: {segments[current_index].text}")
        
        # Add 4 next segments
        end_idx = min(len(segments), current_index + 5)
        for i in range(current_index + 1, end_idx):
            context_segments.append(f"[Next {i - current_index}]: {segments[i].text}")
        
        return "\n".join(context_segments)

    def _create_translation_prompt(self, target_text: str, context_text: str) -> str:
        """
        Create translation prompt with context

        Args:
            target_text: The specific text to translate
            context_text: Surrounding context for better translation

        Returns:
            Formatted prompt for translation
        """
        return f"""Please translate the [Current] segment from Arabic to English. Use the surrounding context to ensure accurate and contextually appropriate translation.

Context:
{context_text}

Instructions:
1. Translate only the [Current] segment text
2. Use the context to understand the meaning and maintain consistency
3. Preserve the original meaning and tone
4. Provide only the English translation without explanations

Translation:"""
