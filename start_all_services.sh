#!/bin/bash

# Start all microservices for chapter detection testing
cd /home/<USER>/website_project

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 启动所有微服务 ===${NC}"

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}等待 $service_name 启动...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$port/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name 已启动并响应${NC}"
            return 0
        fi
        
        echo -e "尝试 $attempt/$max_attempts: 等待 $service_name 响应..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ $service_name 启动超时${NC}"
    return 1
}

# Kill existing processes on the ports
echo -e "${YELLOW}检查并清理现有服务...${NC}"
for port in 8001 8002 8004; do
    if check_port $port; then
        echo -e "${YELLOW}端口 $port 被占用，正在停止现有进程...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
done

# Start Camel Tools API (port 8002)
echo -e "${BLUE}1. 启动 Camel Tools API (端口 8002)...${NC}"
cd packages/services/camel-tools-api
nohup poetry run python src/main.py > ../../../logs/camel-tools.log 2>&1 &
CAMEL_PID=$!
echo "Camel Tools API PID: $CAMEL_PID"
cd ../../..

# Start Qwen Service (port 8004)
echo -e "${BLUE}2. 启动 Qwen Service (端口 8004)...${NC}"
cd packages/services/qwen-service
nohup poetry run python src/main.py > ../../../logs/qwen-service.log 2>&1 &
QWEN_PID=$!
echo "Qwen Service PID: $QWEN_PID"
cd ../../..

# Start Audio Processor (port 8001)
echo -e "${BLUE}3. 启动 Audio Processor (端口 8001)...${NC}"
cd packages/services/audio-processor
nohup poetry run python src/main.py > ../../../logs/audio-processor.log 2>&1 &
AUDIO_PID=$!
echo "Audio Processor PID: $AUDIO_PID"
cd ../../..

# Create logs directory if it doesn't exist
mkdir -p logs

# Save PIDs for later cleanup
echo "$CAMEL_PID" > logs/camel-tools.pid
echo "$QWEN_PID" > logs/qwen-service.pid
echo "$AUDIO_PID" > logs/audio-processor.pid

echo -e "${YELLOW}等待所有服务启动...${NC}"
sleep 5

# Check if services are running
echo -e "${BLUE}=== 检查服务状态 ===${NC}"

# Wait for each service
wait_for_service "Camel Tools API" 8002
CAMEL_STATUS=$?

wait_for_service "Qwen Service" 8004
QWEN_STATUS=$?

wait_for_service "Audio Processor" 8001
AUDIO_STATUS=$?

# Summary
echo -e "${BLUE}=== 服务启动总结 ===${NC}"
if [ $CAMEL_STATUS -eq 0 ]; then
    echo -e "${GREEN}✅ Camel Tools API (8002) - 运行正常${NC}"
else
    echo -e "${RED}❌ Camel Tools API (8002) - 启动失败${NC}"
fi

if [ $QWEN_STATUS -eq 0 ]; then
    echo -e "${GREEN}✅ Qwen Service (8004) - 运行正常${NC}"
else
    echo -e "${RED}❌ Qwen Service (8004) - 启动失败${NC}"
fi

if [ $AUDIO_STATUS -eq 0 ]; then
    echo -e "${GREEN}✅ Audio Processor (8001) - 运行正常${NC}"
else
    echo -e "${RED}❌ Audio Processor (8001) - 启动失败${NC}"
fi

# Check if all services are running
if [ $CAMEL_STATUS -eq 0 ] && [ $QWEN_STATUS -eq 0 ] && [ $AUDIO_STATUS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有服务启动成功！可以开始测试章节检测功能。${NC}"
    echo -e "${BLUE}运行以下命令进行测试:${NC}"
    echo -e "  ./test_chapter_detection.sh"
    echo -e ""
    echo -e "${BLUE}查看日志:${NC}"
    echo -e "  tail -f logs/camel-tools.log"
    echo -e "  tail -f logs/qwen-service.log"
    echo -e "  tail -f logs/audio-processor.log"
    echo -e ""
    echo -e "${BLUE}停止所有服务:${NC}"
    echo -e "  ./stop_all_services.sh"
else
    echo -e "${RED}❌ 部分服务启动失败，请检查日志文件${NC}"
    echo -e "日志文件位置:"
    echo -e "  logs/camel-tools.log"
    echo -e "  logs/qwen-service.log"
    echo -e "  logs/audio-processor.log"
fi
