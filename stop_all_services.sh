#!/bin/bash

# Stop all microservices
cd /home/<USER>/website_project

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 停止所有微服务 ===${NC}"

# Function to stop service by PID file
stop_service_by_pid() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${YELLOW}停止 $service_name (PID: $pid)...${NC}"
            kill "$pid"
            sleep 2
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "${YELLOW}强制停止 $service_name...${NC}"
                kill -9 "$pid"
            fi
            
            echo -e "${GRE<PERSON>}✅ $service_name 已停止${NC}"
        else
            echo -e "${YELLOW}$service_name 进程不存在${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}$service_name PID文件不存在${NC}"
    fi
}

# Function to stop service by port
stop_service_by_port() {
    local service_name=$1
    local port=$2
    
    local pids=$(lsof -ti:$port 2>/dev/null)
    if [ -n "$pids" ]; then
        echo -e "${YELLOW}停止端口 $port 上的 $service_name...${NC}"
        echo "$pids" | xargs kill 2>/dev/null
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            echo -e "${YELLOW}强制停止端口 $port 上的进程...${NC}"
            echo "$remaining_pids" | xargs kill -9 2>/dev/null
        fi
        
        echo -e "${GREEN}✅ $service_name (端口 $port) 已停止${NC}"
    else
        echo -e "${YELLOW}端口 $port 上没有运行的进程${NC}"
    fi
}

# Stop services by PID files first
if [ -d "logs" ]; then
    stop_service_by_pid "Camel Tools API" "logs/camel-tools.pid"
    stop_service_by_pid "Qwen Service" "logs/qwen-service.pid"
    stop_service_by_pid "Audio Processor" "logs/audio-processor.pid"
fi

# Stop services by port as backup
stop_service_by_port "Camel Tools API" 8002
stop_service_by_port "Qwen Service" 8004
stop_service_by_port "Audio Processor" 8001

echo -e "${GREEN}🛑 所有服务已停止${NC}"
